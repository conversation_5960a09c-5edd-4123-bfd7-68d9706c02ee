from curl_cffi import requests
# from config import load_config # Removed import
import json
from datetime import datetime
import random # 新增导入
import string # 新增导入
import time # 新增导入
import os # 导入os模块用于文件路径操作
import threading # Import threading for Lock

# 新增：getBookingCart GraphQL 查询
GET_BOOKING_CART_QUERY = """
query getBookingCart {
  getBookingCart {
    originalResponse
    __typename
  }
}
"""

class VAApiClient:
    def __init__(self):
        # print("[VAApiClient DEBUG] Initializing...") # Early debug
        # self.config = load_config() # Ensure this doesn't cause issues if config is missing
        self.session = None # Initialize session attribute
        self.proxies = []
        self.use_proxy = False
        self.current_proxy_index = -1
        self.token = None
        self.execution_id = None
        self.user_agent_override = None # For specific UA needs beyond impersonation
        # print("[*] VAApiClient: Attempting to initialize curl_cffi session...")
        try:
            # Use curl_cffi session with impersonate
            self.session = requests.Session(impersonate="chrome101")
            # print(f"[*] VAApiClient: Successfully initialized curl_cffi session. Type: {type(self.session)}, Impersonation: chrome101")
            # Optionally, set a default User-Agent if not relying solely on impersonate or if UA override is not yet set.
            # self.session.headers['User-Agent'] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
            # print(f"[*] VAApiClient User-Agent on init: {self.session.headers.get('User-Agent')}")
        except Exception as e_init_session:
            error_message = f"[!!!] VAApiClient: CRITICAL - Failed to initialize curl_cffi session: {e_init_session}"
            print(error_message)
            # Potentially re-raise or handle so the instance is not used if session is critical
            # self.session = None # Ensure session is None if init fails - No longer needed if we raise
            raise RuntimeError(error_message) from e_init_session

        self.session_lock = threading.Lock()
        self.base_url = "https://book.virginaustralia.com/api/graphql"
        self.initial_ibeopentoken_value = None # DEPRECATED: Superseded by explicit_cookies/auth_data model
        self.saved_dcsessionid = None # DEPRECATED: Superseded by explicit_cookies/auth_data model
        self.saved_dcsessionid_domain = None # DEPRECATED
        self.saved_dcsessionid_path = None # DEPRECATED

        self.shopping_basket_hash_code = None # 在 book_flight 中设置
        self.current_passengers_info = None # 在 book_flight 中设置
        self.confirmed_cash_amount = None
        self.confirmed_cash_currency = None
        self.confirmed_award_amount = None
        self.device_fingerprint_id = None

        self.default_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
        if self.session: # Set default UA based on impersonation if successful
             self.session.headers["User-Agent"] = self.default_user_agent

        self.load_proxies()
        if self.proxies:
            # print(f"[*] VAApiClient: Proxies loaded ({len(self.proxies)}). Initial proxy: {self.proxies[self.current_proxy_index].get('http') if self.current_proxy_index != -1 else 'None Set'}")
            pass # Logging already done by load_proxies
        else:
            # print("[*] VAApiClient: No proxies loaded or proxy file not found.")
            pass
        print(f"[*] VAApiClient User-Agent on init: {self.session.headers.get('User-Agent', 'Default Requests UA')}")
        if self.user_agent_override: print(f"[*] VAApiClient User-Agent will be overridden by: {self.user_agent_override}")

    def load_proxies(self):
        print("[*] VAApiClient: Attempting to load proxies...")
        self.proxies = [] # Ensure proxies list is reset before loading
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "proxy_config.json"),
                os.path.join(current_dir, "..", "proxy_config.json"),
                "proxy_config.json"
            ]
            proxy_config_path = next((path for path in possible_paths if os.path.exists(path)), None)
            if not proxy_config_path:
                print("[!] VAApiClient: Proxy config file not found.")
                return

            with open(proxy_config_path, 'r', encoding='utf-8') as f:
                proxy_list_from_file = json.load(f)

            if not isinstance(proxy_list_from_file, list):
                print(f"[!] VAApiClient: proxy_config.json is not a JSON list. Loaded type: {type(proxy_list_from_file)}")
                return

            for idx, proxy_entry_str in enumerate(proxy_list_from_file):
                if not (proxy_entry_str and isinstance(proxy_entry_str, str)):
                    print(f"    [PROXY LOAD WARN] Entry {idx} is not a valid string: {proxy_entry_str}")
                    continue
                parts = proxy_entry_str.strip().split(':') # Added strip()
                proxy_url = None
                if len(parts) == 2:
                    host, port = parts[0], parts[1]
                    proxy_url = f"http://{host}:{port}"
                    # print(f"    [PROXY LOAD] Parsed host:port (Entry {idx}): {proxy_url} from '{proxy_entry_str}'")
                elif len(parts) == 4:
                    host, port, user, passwd = parts[0], parts[1], parts[2], parts[3]
                    # Log parsed credentials carefully
                    # print(f"    [PROXY LOAD] (Entry {idx}) Original: '{proxy_entry_str}' -> Parsed User: '{user}', Pass: '{passwd[:2]}***{passwd[-2:] if len(passwd)>3 else ''}', Host: {host}, Port: {port}")
                    proxy_url = f"http://{user}:{passwd}@{host}:{port}"
                else:
                    print(f"    [PROXY LOAD WARN] Entry {idx} '{proxy_entry_str}' has unexpected format (parts: {len(parts)}). Skipping.")
                    continue
                if proxy_url: self.proxies.append({"http": proxy_url, "https": proxy_url})

            if self.proxies:
                print(f"[*] VAApiClient: Successfully loaded {len(self.proxies)} proxies from {proxy_config_path}.")
            else:
                print("[!] VAApiClient: No valid proxies parsed from config file or file was empty/invalid.")
        except FileNotFoundError:
            print("[!] VAApiClient: proxy_config.json not found (FileNotFoundError).")
        except json.JSONDecodeError as e_json:
            print(f"[!] VAApiClient: Error decoding proxy_config.json: {e_json}")
        except Exception as e:
            print(f"[!] VAApiClient: Unexpected error loading proxies: {e}")
            import traceback; traceback.print_exc()

    def set_proxy(self, index=None):
        if not self.proxies:
            # print("[VAApiClient] Proxy list is empty, cannot set proxy.") # Can be noisy if called often
            self.session.proxies = {}
            self.current_proxy_index = -1
            return False

        if index is not None:
            if 0 <= index < len(self.proxies):
                self.current_proxy_index = index
            else:
                print(f"[!] VAApiClient: Proxy index {index} out of range. Using random.")
                self.current_proxy_index = random.randint(0, len(self.proxies) - 1)
        else: # Random selection if no index
            if len(self.proxies) == 1:
                self.current_proxy_index = 0
            else: # More than one proxy, try to pick a different one if one was already set
                if self.current_proxy_index != -1 and len(self.proxies) > 1:
                    new_idx = self.current_proxy_index
                    while new_idx == self.current_proxy_index: # Ensure it's different
                        new_idx = random.randint(0, len(self.proxies) - 1)
                    self.current_proxy_index = new_idx
                else: # First time or only one proxy
                    self.current_proxy_index = random.randint(0, len(self.proxies) - 1)

        self.session.proxies = self.proxies[self.current_proxy_index]
        active_proxy_url = list(self.session.proxies.values())[0]
        print(f"[*] VAApiClient: Proxy set to index {self.current_proxy_index} - {active_proxy_url}")
        return True

    def rotate_proxy(self):
        """随机轮换代理"""
        if not self.proxies:
            print("[!] 代理列表为空，无法轮换代理")
            return False

        # 随机选择一个不同的代理
        if len(self.proxies) > 1:
            # 如果有多个代理，确保选择一个不同的代理
            new_index = self.current_proxy_index
            while new_index == self.current_proxy_index:
                new_index = random.randint(0, len(self.proxies) - 1)
            self.current_proxy_index = new_index
        else:
            # 如果只有一个代理，仍然使用它
            self.current_proxy_index = 0

        # 设置代理
        self.session.proxies = self.proxies[self.current_proxy_index]
        print(f"[*] 已随机轮换到新代理: {list(self.session.proxies.values())[0]}")
        return True

    def set_cookies_from_list(self, cookies_list):
        """直接从列表设置 session cookies"""
        with self.session_lock: # Acquire lock for the whole method
            self.session.cookies.clear() # 清空旧 cookies
            if not cookies_list:
                print("[!] 传入的 cookie 列表为空。")
                return False
            try:
                # 先收集所有cookie，按名称分组，确保每个名称只有一个cookie
                cookie_dict = {}
                for cookie in cookies_list:
                    cookie_name = cookie['name']
                    # 如果有多个同名cookie，使用最后一个
                    cookie_dict[cookie_name] = cookie

                # 设置去重后的cookies
                for cookie_name, cookie in cookie_dict.items():
                    # requests session 需要 domain 参数，如果 cookie 中没有，尝试从 URL 推断或省略
                    cookie_domain = cookie.get('domain')
                    # 确保 domain 不为空字符串
                    if cookie_domain == "":
                        cookie_domain = None
                    # 如果 domain 以 '.' 开头，requests 可能不需要它
                    if cookie_domain and cookie_domain.startswith('.'):
                        cookie_domain = cookie_domain[1:]

                    self.session.cookies.set(
                        cookie['name'],
                        cookie['value'],
                        domain=cookie_domain,
                        path=cookie.get('path', '/') # 提供默认 path
                    )

                print(f"[*] 已从列表加载 {len(cookie_dict)} 个唯一 cookies 到 session (原始列表长度: {len(cookies_list)})。")
                return True
            except Exception as e:
                import traceback
                print(f"[!] 从列表设置 cookie 失败: {e}")
                print(traceback.format_exc()) # 打印详细的错误堆栈信息
                return False

    def set_device_fingerprint_id(self, device_fingerprint_id):
        """设置设备指纹ID，用于支付请求"""
        if not device_fingerprint_id:
            print("[!] 设备指纹ID不能为空")
            return False

        self.device_fingerprint_id = device_fingerprint_id
        print(f"[*] 已设置设备指纹ID: {device_fingerprint_id[:10]}...")
        return True

    # 移除 reload_cookies 方法
    # def reload_cookies(self): ...

    def _graphql_request(self, operation_name, variables, query,
                         task_token=None, task_execution_id=None,
                         explicit_cookies_for_request: dict = None,
                         task_user_agent=None,
                         use_specific_proxy=None, # This is a proxy dict for self.session.proxies
                         return_headers=False):
        # 简化调试输出
        with self.session_lock:
            current_token_for_auth_header = task_token if task_token is not None else self.token
            current_execution_id_for_header = task_execution_id if task_execution_id is not None else self.execution_id

            effective_user_agent = task_user_agent or self.user_agent_override or self.default_user_agent
            if self.session and not effective_user_agent:
                effective_user_agent = self.session.headers.get("User-Agent")
            elif not effective_user_agent:
                effective_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.0.0 Safari/537.36"

            original_session_cookies = self.session.cookies.copy()
            original_session_proxies = self.session.proxies.copy()
            self.session.cookies.clear() # Crucial: start with a clean slate for cookies for this request

            applied_cookies_names = []
            if explicit_cookies_for_request and isinstance(explicit_cookies_for_request, dict):
                for name, value in explicit_cookies_for_request.items():
                    domain_for_cookie = None; path_for_cookie = '/'
                    if name.lower() == 'reese84': domain_for_cookie = "book.virginaustralia.com"
                    # For bookingAirSearch, we ONLY want reese84 from explicit_cookies.
                    # Other cookies like ibeopentoken, dcsessionid should NOT be added here if it's a search.
                    # This logic is implicitly handled if explicit_cookies_for_request *only* contains reese84 for search.

                    cookie_value_to_set = value
                    cookie_domain_to_set = domain_for_cookie
                    cookie_path_to_set = path_for_cookie

                    if isinstance(value, dict):
                        cookie_value_to_set = value.get('value')
                        cookie_domain_to_set = value.get('domain', domain_for_cookie)
                        cookie_path_to_set = value.get('path', path_for_cookie)

                    if cookie_value_to_set is not None:
                        self.session.cookies.set(name, cookie_value_to_set, domain=cookie_domain_to_set, path=cookie_path_to_set)
                        applied_cookies_names.append(name)

                if applied_cookies_names: print(f"[*] Applied {len(applied_cookies_names)} explicit cookies for {operation_name}. Applied: {applied_cookies_names}")
            else:
                print(f"[*] No explicit_cookies_for_request for {operation_name}.")

            # Standard headers, attempt to match demo.py as closely as possible for bookingAirSearch
            standard_headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "adrum": "isAjax:true",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "origin": "https://book.virginaustralia.com",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "referer": "https://book.virginaustralia.com/dx/VADX/",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-sabre-storefront": "VADX",
                # Removed "application-id" for now to align closer with demo.py successful call structure
            }

            if operation_name == "bookingAirSearch":
                # For bookingAirSearch, use headers and UA identical to demo.py successful standalone call
                headers = standard_headers.copy()
                headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
                headers["sec-ch-ua"] = '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
                headers["sec-ch-ua-mobile"] = "?0"
                headers["sec-ch-ua-platform"] = '"Windows"'
                # print(f"[*] bookingAirSearch: Using fixed demo.py User-Agent and sec-ch-ua headers.")
                # Authorization and execution headers are correctly omitted for bookingAirSearch by not adding them here.
            else:
                # For other operations, use the effective_user_agent logic (from reese or default)
                headers = standard_headers.copy()
                headers["user-agent"] = effective_user_agent
                # Use sec-ch-ua consistent with impersonate chrome101 if not bookingAirSearch
                headers["sec-ch-ua"] = '" Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"'
                headers["sec-ch-ua-mobile"] = "?0"
                headers["sec-ch-ua-platform"] = '"Windows"'

                # Add Authorization and execution headers ONLY for non-search operations and if available
                if current_token_for_auth_header:
                    headers["Authorization"] = f"Bearer {current_token_for_auth_header}"
                if current_execution_id_for_header:
                    headers["execution"] = current_execution_id_for_header

            if operation_name == "bookingAirSearch" and "airSearchInput" in variables: # Date headers for search
                try:
                    s_date = datetime.strptime(variables["airSearchInput"]["itineraryParts"][0]["when"]["date"], "%Y-%m-%d")
                    headers["activeMonth"] = s_date.strftime("%m-%d-%Y")
                    headers["date"] = s_date.strftime("%m-%d-%Y")
                except Exception: pass

            payload_dict = {"operationName": operation_name, "variables": variables, "query": query}
            if "extensions" not in payload_dict: payload_dict["extensions"] = {}

            # Convert payload to string for `data` param if `json` param is not used by curl_cffi for this content-type
            # curl_cffi's `json` param works like requests, handles content-type and serialization.
            # However, demo.py used `data=json.dumps(...)`. Let's stick to `json=` for curl_cffi.requests.
            # If issues, can switch to `data=json.dumps(payload_dict, separators=(',', ':'))` and ensure `content-type` is `application/json`.

            timeout = 60 if operation_name == "bookingPurchase" else 30
            active_proxy_for_log = "None (Direct)" # Default log message for proxy

            if use_specific_proxy is not None and isinstance(use_specific_proxy, dict):
                # Caller explicitly provided a proxy setting (can be {} for no proxy)
                self.session.proxies = use_specific_proxy
                if use_specific_proxy: # If not an empty dict
                    active_proxy_for_log = use_specific_proxy.get('http') or use_specific_proxy.get('https') or "SpecificProxyInvalidFormat"
                # If use_specific_proxy is {}, active_proxy_for_log remains "None (Direct)"
            elif operation_name == "bookingAirSearch": # For bookingAirSearch, explicitly NO proxy unless specified by use_specific_proxy
                # This means high_concurrency_crawler should pass {} if it wants direct for search_flights
                # which it is currently doing.
                # If use_specific_proxy was None (default), then no proxy here.
                self.session.proxies = {} # Default to no proxy for search if not specified
                active_proxy_for_log = "None (Direct) - Search Default"
            elif self.use_proxy and self.proxies: # For non-search operations, use internal rotation if enabled
                self.set_proxy() # Rotate proxy from internal list
                if self.session.proxies:
                    active_proxy_for_log = self.session.proxies.get('http') or self.session.proxies.get('https') or "RotatedProxyInvalidFormat"
            else:
                # Default to no proxy if no specific instruction and not a search op eligible for rotation
                self.session.proxies = {}

            # 发送请求
            resp = None
            try:
                resp = self.session.post(self.base_url, headers=headers, json=payload_dict, timeout=timeout)
            except Exception as e:
                print(f"[!!!] _graphql_request: Exception during {operation_name} (Proxy: {active_proxy_for_log or 'None'}): {type(e).__name__} - {e}")
                # Ensure original state is restored even if post fails, then re-raise
                self.session.cookies.clear()
                self.session.cookies.update(original_session_cookies)
                self.session.proxies = original_session_proxies
                raise

            # Process response if request was successful (no exception from post)
            response_json = None
            http_error_raised = None # Flag to see if http_err was raised by raise_for_status
            try:
                if resp is None: # Should only happen if post() itself failed and raised, caught above
                    raise requests.errors.RequestException("Response object is None after POST, indicates prior error.")
                resp.raise_for_status() # Check for HTTP errors 4xx/5xx
                response_json = resp.json() # Try to parse JSON

            except requests.errors.HTTPError as http_err:
                http_error_raised = http_err # Store the error
                err_content = resp.text if resp is not None else "No response object for HTTPError"
                status_code = resp.status_code if resp is not None else "N/A"
                print(f"[!!!] _gql HTTP error for {operation_name} ({status_code}): {http_err}. Response: {err_content[:300]}...")
                try:
                    response_json = resp.json() if resp is not None else {"errors": [{"message": f"HTTP error {status_code}"}]}
                except json.JSONDecodeError:
                    response_json = {"errors": [{"message": f"HTTP error {status_code}", "details": err_content}]}
                # We will re-raise http_error_raised later, after finally block

            except json.JSONDecodeError as json_err:
                status_code_json = resp.status_code if resp is not None else "N/A"
                resp_text_json = resp.text if resp is not None else "No response object for JSONDecodeError"
                print(f"[!!!] _gql JSON decode error for {operation_name} (Status: {status_code_json}): {json_err}. Text: {resp_text_json[:300]}...")
                response_json = {"errors": [{"message": "JSON decode error", "details": resp_text_json[:300]}]}
                # This is a successful HTTP status but bad JSON. We might not want to raise an exception for the caller here,
                # but return the error structure so caller can decide.

            except Exception as generic_resp_err: # Catch other unexpected errors during response processing
                 status_code_gen = resp.status_code if resp is not None else "N/A"
                 resp_text_gen = resp.text if resp is not None else "No response object for generic error"
                 print(f"[!!!] _gql Generic error processing response for {operation_name} (Status: {status_code_gen}): {generic_resp_err}. Text: {resp_text_gen[:300]}...")
                 response_json = {"errors": [{"message": f"Generic response processing error: {generic_resp_err}", "details": resp_text_gen[:300]}]}
                 # This could be more severe, consider re-raising or specific handling.

            # The session_lock is for the entire _graphql_request method due to `with self.session_lock:` at the start.
            # The original_session_cookies and original_session_proxies are restored by the `with` statement's exit.
            # So, no explicit finally block needed here just for restoring those.

            if http_error_raised: # If raise_for_status() triggered an HTTPError, re-raise it now
                raise http_error_raised

            if return_headers:
                return response_json, resp.headers if resp is not None else None
            return response_json

    def _call_gql(self, operation_name, variables, query, auth_data, explicit_cookies=None, use_specific_proxy=None, return_headers=False):
        return self._graphql_request(operation_name, variables, query,
                                     task_token=auth_data.get('token'),
                                     task_execution_id=auth_data.get('execution_id'),
                                     explicit_cookies_for_request=explicit_cookies,
                                     task_user_agent=auth_data.get('user_agent'),
                                     use_specific_proxy=use_specific_proxy, # Pass it down
                                     return_headers=return_headers)

    def login(self):
        # token已通过playwright获取，此处无需登录
        print("[!] login 方法已弃用。Token和Execution ID应通过外部方式设置。")
        return False

    def search_flights(self, origin, destination, date, cabin="Business", award_booking=True,
                       auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        query = """query bookingAirSearch($airSearchInput: CustomAirSearchInput) { bookingAirSearch(airSearchInput: $airSearchInput) { originalResponse __typename } }"""
        variables = {"airSearchInput": {"cabinClass": cabin, "awardBooking": award_booking, "promoCodes": [], "searchType": "BRANDED", "itineraryParts": [{"from": {"useNearbyLocations": False, "code": origin}, "to": {"useNearbyLocations": False, "code": destination}, "when": {"date": date}}], "passengers": {"ADT": 1}}}
        return self._call_gql("bookingAirSearch", variables, query, auth_data,
                              explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)

    def book_flight(self, selected_offer, passengers_info_list: list,
                    award_amount_ref=None, cash_amount_ref=None, cash_currency_ref=None,
                    auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        self.confirmed_award_amount = int(award_amount_ref) if award_amount_ref is not None else None
        self.confirmed_cash_amount = float(cash_amount_ref) if cash_amount_ref is not None else None
        self.confirmed_cash_currency = cash_currency_ref

        current_shopping_basket_hash_code = selected_offer.get('shoppingBasketHashCode')
        if not current_shopping_basket_code:
            try:
                brand_offers = selected_offer.get('brandedResults', {}).get('itineraryPartBrands', [{}])[0].get('brandOffers', [])
                if brand_offers and isinstance(brand_offers, list) and brand_offers: # Ensure brand_offers is not an empty list
                    current_shopping_basket_hash_code = brand_offers[0].get('shoppingBasketHashCode')
            except Exception as e_hash_extract:
                print(f"[API Client WARN] Error extracting hash from brandOffers: {e_hash_extract}")
                # Pass, so current_shopping_basket_hash_code remains None if extraction failed
                pass

        if not current_shopping_basket_hash_code:
            print(f"[API Client ERROR] No shoppingBasketHashCode found in selected_offer or brandOffers. Offer: {str(selected_offer)[:300]}...")
            return False, "No shoppingBasketHashCode in offer", "NO_HASH"
        self.shopping_basket_hash_code = str(current_shopping_basket_hash_code)

        # Pass use_specific_proxy to subsequent calls
        add_itin_resp = self.add_itinerary(self.shopping_basket_hash_code, auth_data, explicit_cookies, use_specific_proxy)
        if not add_itin_resp or add_itin_resp.get("errors"):
            err = add_itin_resp.get("errors",[{}])[0].get("message") if add_itin_resp and add_itin_resp.get("errors") else "Add itinerary failed"
            return False, f"Add itinerary: {err}", "ADD_ITIN_FAILED"
        updated_hash = add_itin_resp.get("data",{}).get("bookingAddItinerary",{}).get("originalResponse",{}).get("shoppingBasketHashCode")
        if updated_hash: self.shopping_basket_hash_code = str(updated_hash)

        cart_resp = self._get_booking_cart(auth_data, explicit_cookies, use_specific_proxy)
        if not cart_resp or cart_resp.get("errors"): return False, "Get cart after add_itin failed", "GET_CART_FAILED"

        update_pax_resp = self.update_passengers(passengers_info_list, auth_data, explicit_cookies, use_specific_proxy)
        if not update_pax_resp or update_pax_resp.get("errors"):
            err_pax = update_pax_resp.get("errors",[{}])[0].get("message") if update_pax_resp and update_pax_resp.get("errors") else "Update pax failed"
            return False, f"Update passengers: {err_pax}", "UPDATE_PAX_FAILED"

        if self.confirmed_award_amount is None: return False, "Award amount not set for confirm", "AWARD_NOT_SET"
        confirm_success, confirm_msg, confirm_err_code = self._confirm_award_payment_details(self.confirmed_award_amount, auth_data, explicit_cookies, use_specific_proxy)
        if not confirm_success: return False, f"Confirm payment details: {confirm_msg}", confirm_err_code

        self.current_passengers_info = passengers_info_list
        return True, {"message": "Pre-payment steps OK", "confirmed_payment": {"award": self.confirmed_award_amount, "cash": self.confirmed_cash_amount, "currency": self.confirmed_cash_currency}}, None

    def make_payment(self, payment_config_dict: dict, passengers_info_list: list,
                     award_amount_ref: int, cash_amount_ref: float, cash_currency_ref: str="AUD",
                     use_points_for_tax_val: bool=False, auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        actual_award = self.confirmed_award_amount if self.confirmed_award_amount is not None else award_amount_ref
        actual_cash = self.confirmed_cash_amount if self.confirmed_cash_amount is not None else cash_amount_ref
        actual_currency = self.confirmed_cash_currency if self.confirmed_cash_currency else cash_currency_ref
        card_number = payment_config_dict.get("card_number")
        if not use_points_for_tax_val and actual_cash > 0.0 and not card_number: return False, "Card info missing for cash payment", "CARD_MISSING"
        payment_objects_list = [{
            "@type": "AWARD", "amount": {"amount": int(actual_award), "currency": "FFCURRENCY"}, "paymentCode": "AWARD"
        }]
        if not use_points_for_tax_val and actual_cash > 0.0:
            card_code = "VI"; expiry_month = payment_config_dict.get("expiry_month", "01"); expiry_year = payment_config_dict.get("expiry_year", "1900")
            if card_number.startswith("5"): card_code = "CA"
            elif card_number.startswith("3"): card_code = "AX"
            payment_objects_list.append({
                "@type": "CREDIT_CARD", "number": card_number, "cvc": payment_config_dict.get("cvv","000"),
                "holderName": payment_config_dict.get("billing_address",{}).get("holder_name", f"{passengers_info_list[0].get('first_name','F')} {passengers_info_list[0].get('last_name','L')}"),
                "expirationDate": f"{expiry_year}-{int(expiry_month):02d}", "cardCode": card_code,
                "amount": {"amount": float(actual_cash), "currency": actual_currency}, "paymentCode": card_code
            })
        billing_data = {
            "street1": payment_config_dict.get("billing_address",{}).get("street", "1 Street"),
            "city": payment_config_dict.get("billing_address",{}).get("city", "City"),
            "country": payment_config_dict.get("billing_address",{}).get("country_code", "AU"),
            "phone": {"countryCode": "61", "number": payment_config_dict.get("billing_address",{}).get("phone_number", passengers_info_list[0].get('phone','0400000000'))}
        }
        variables = {"payment": payment_objects_list, "billingData": billing_data, "paymentRequired": True if actual_cash > 0.0 and not use_points_for_tax_val else False, "languageForBooking": "en_GB", "fraudNetData": {"deviceFingerPrintId": self.device_fingerprint_id or f"va-ph-{int(time.time())}"}, "remarksAndSSRs": {"remarks": ["h-dxr/flow/b2c-desktop", "h-dxr/channel/none"]}}
        query = """mutation BookingPurchase($payment: [JSONObject!]!, $billingData: JSONObject, $paymentRequired: Boolean, $languageForBooking: String, $fraudNetData: JSONObject, $remarksAndSSRs: JSONObject) { bookingPurchase(payment: $payment, billingData: $billingData, paymentRequired: $paymentRequired, languageForBooking: $languageForBooking, fraudNetData: $fraudNetData, remarksAndSSRs: $remarksAndSSRs) { originalResponse __typename } }"""
        gql_response = self._call_gql("BookingPurchase", variables, query, auth_data, explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)
        final_purchase_result = gql_response.get("data", {}).get("bookingPurchase")
        if not final_purchase_result:
            err_msg = gql_response.get("errors",[{}])[0].get("message") if gql_response.get("errors") else "Purchase GQL error"
            return False, err_msg, "PURCHASE_GQL_ERROR"
        original_resp_content = final_purchase_result.get("originalResponse", {})
        messages = original_resp_content.get("messages", [])
        biz_errors = [m for m in messages if isinstance(m, dict) and m.get("type", "").upper() == "ERROR"]
        if biz_errors:
            return False, biz_errors[0].get("text", "Payment business error"), "PAYMENT_BIZ_ERROR"
        booking_ref = original_resp_content.get("bookingReference")
        return True, {"purchase_response": gql_response, "booking_reference": booking_ref}, None

    def _get_booking_cart(self, auth_data, explicit_cookies=None, use_specific_proxy=None):
        return self._call_gql("getBookingCart", {}, GET_BOOKING_CART_QUERY, auth_data,
                              explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)

    def add_itinerary(self, flight_selection_id_val: str, auth_data, explicit_cookies=None, use_specific_proxy=None):
        query = """query bookingAddItinerary($selectFlights: String!) { bookingAddItinerary(selectFlights: $selectFlights) { originalResponse __typename } }"""
        variables = {"selectFlights": f"selectFlights={flight_selection_id_val}"}
        return self._call_gql("bookingAddItinerary", variables, query, auth_data,
                              explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)

    def update_passengers(self, passengers_info_list: list, auth_data, explicit_cookies=None, use_specific_proxy=None):
        contact_email = passengers_info_list[0].get("email"); contact_phone = passengers_info_list[0].get("phone")
        passengers_gql = []
        for i, p in enumerate(passengers_info_list):
            passengers_gql.append({"passengerIndex": i + 1, "passengerDetails": {"firstName": p.get("first_name"), "lastName": p.get("last_name"), "prefix": p.get("title")}, "passengerInfo": {"gender": p.get("gender"), "dateOfBirth": p.get("dob"), "type": "ADT", "emails": [contact_email], "phones": [{"type": "MOBILE", "number": contact_phone}]}, "preferences": {"specialPreferences": {"specialRequests": []}, "frequentFlyer": []}})
        variables = {"contact": {"emails": [contact_email], "phones": [{"type": "MOBILE", "number": contact_phone}]}, "passengers": passengers_gql}
        query = """mutation bookingUpdatePassengers($contact: ContactDetailsInput, $passengers: [PassengerInput]) { bookingUpdatePassengers(contact: $contact, passengers: $passengers) { originalResponse __typename } }"""
        return self._call_gql("bookingUpdatePassengers", variables, query, auth_data,
                              explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)

    def _confirm_award_payment_details(self, award_amount_to_confirm, auth_data, explicit_cookies=None, use_specific_proxy=None):
        query = """mutation bookingPaymentDetails($paymentType: String!, $paymentDetailsInput: PaymentDetailsInput) { b2cPaymentDetails: bookingPaymentDetails(paymentType: $paymentType, paymentDetailsInput: $paymentDetailsInput) { originalResponse __typename } }"""
        variables = {"paymentType": "AWARD", "paymentDetailsInput": {"awardDetailsSelection": {"fareAmount": {"amount": int(award_amount_to_confirm), "currency": "FFCURRENCY"}, "ancillaryToggleSelections": []}}}
        gql_response = self._call_gql("bookingPaymentDetails", variables, query, auth_data,
                                      explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)
        if gql_response.get("errors"): return False, gql_response.get("errors")[0].get("message","GQL error in confirm"), "GQL_ERROR"
        original_response = gql_response.get("data", {}).get("b2cPaymentDetails", {}).get("originalResponse", {})
        messages = original_response.get("messages", [])
        biz_errors = [m for m in messages if isinstance(m, dict) and m.get("type", "").upper() == "ERROR"]
        if biz_errors: return False, biz_errors[0].get("message", "Business error in confirm"), "BUSINESS_ERROR"
        try:
            price_info = original_response.get("price", {})
            total_alts = price_info.get("total", {}).get("alternatives", [])
            if total_alts and isinstance(total_alts[0], list) and total_alts[0]:
                for item in total_alts[0]:
                    if item.get("currency") != "FFCURRENCY":
                        self.confirmed_cash_amount = float(item.get("amount"))
                        self.confirmed_cash_currency = item.get("currency")
                        break
        except: pass
        return True, "Confirmed payment details successfully", None

    def _log_request_details(self, url, headers, cookies, payload, operation_name, proxy):
        if operation_name not in ["bookingAirSearch"] or getattr(self, 'debug_mode', False):
             print(f"[API REQ] Op: {operation_name}, Proxy: {proxy}, Token: {headers.get('Authorization','N/A')[:20]}..., ExecID: {headers.get('execution','N/A')}")
