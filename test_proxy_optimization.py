#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理优化测试脚本
"""

import time
import random
from proxy_manager import ProxyManager
from high_concurrency_crawler import HighConcurrencyCrawler
from api_client import VAApiClient


def test_proxy_manager():
    """测试代理管理器功能"""
    print("=== 测试代理管理器 ===")
    
    pm = ProxyManager()
    
    # 显示初始统计
    stats = pm.get_proxy_stats()
    print(f"初始代理统计: {stats}")
    
    # 测试获取健康代理
    print("\n测试获取健康代理:")
    for i in range(5):
        proxy = pm.get_healthy_proxy()
        if proxy:
            print(f"代理 {i+1}: {proxy.get('http', 'Direct')}")
            
            # 模拟使用结果
            success = random.choice([True, False])
            response_time = random.uniform(0.5, 5.0)
            pm.record_proxy_result(proxy, success, response_time)
            print(f"  结果: {'成功' if success else '失败'}, 响应时间: {response_time:.2f}s")
        else:
            print(f"代理 {i+1}: 无可用代理")
    
    # 显示更新后的统计
    stats = pm.get_proxy_stats()
    print(f"\n更新后代理统计: {stats}")
    
    # 测试排除代理功能
    print("\n测试排除代理功能:")
    proxy1 = pm.get_healthy_proxy()
    proxy2 = pm.get_healthy_proxy(exclude_proxies=[proxy1])
    
    print(f"代理1: {proxy1.get('http', 'Direct') if proxy1 else 'None'}")
    print(f"代理2 (排除代理1): {proxy2.get('http', 'Direct') if proxy2 else 'None'}")
    
    return pm


def test_crawler_with_optimized_proxy():
    """测试使用优化代理的爬虫"""
    print("\n=== 测试优化代理爬虫 ===")
    
    # 创建API客户端配置
    api_client = VAApiClient()
    api_client.use_proxy = True
    
    # 创建爬虫实例
    crawler = HighConcurrencyCrawler(max_workers=3, max_retries=2)
    
    # 添加测试目标
    crawler.add_target("SYD", "MEL", "20250301", "VA123")
    
    # 测试代理选择
    print("\n测试代理选择:")
    for i in range(3):
        reese84_proxy = crawler._get_healthy_proxy()
        api_proxy = crawler._get_healthy_proxy(exclude_proxy=reese84_proxy)
        
        print(f"测试 {i+1}:")
        print(f"  Reese84代理: {reese84_proxy.get('http', 'Direct') if reese84_proxy else 'None'}")
        print(f"  API代理: {api_proxy.get('http', 'Direct') if api_proxy else 'None'}")
        
        # 模拟使用结果
        if reese84_proxy:
            crawler._record_proxy_result(reese84_proxy, random.choice([True, False]))
        if api_proxy:
            crawler._record_proxy_result(api_proxy, random.choice([True, False]), random.uniform(1.0, 3.0))
    
    # 显示代理统计
    stats = crawler.proxy_manager.get_proxy_stats()
    print(f"\n爬虫代理统计: {stats}")
    
    return crawler


def simulate_high_load_scenario():
    """模拟高负载场景"""
    print("\n=== 模拟高负载场景 ===")
    
    pm = ProxyManager()
    
    # 模拟大量请求
    total_requests = 50
    success_count = 0
    
    print(f"模拟 {total_requests} 个请求...")
    
    for i in range(total_requests):
        proxy = pm.get_healthy_proxy()
        if proxy:
            # 模拟请求结果（80%成功率）
            success = random.random() < 0.8
            response_time = random.uniform(0.5, 8.0)
            
            pm.record_proxy_result(proxy, success, response_time)
            
            if success:
                success_count += 1
            
            # 每10个请求显示一次统计
            if (i + 1) % 10 == 0:
                stats = pm.get_proxy_stats()
                print(f"请求 {i+1}: 成功率 {success_count/(i+1)*100:.1f}%, 代理统计: {stats}")
        
        # 模拟请求间隔
        time.sleep(0.1)
    
    # 最终统计
    final_stats = pm.get_proxy_stats()
    print(f"\n最终结果:")
    print(f"总请求数: {total_requests}")
    print(f"成功请求数: {success_count}")
    print(f"成功率: {success_count/total_requests*100:.1f}%")
    print(f"代理统计: {final_stats}")


def test_proxy_recovery():
    """测试代理恢复功能"""
    print("\n=== 测试代理恢复功能 ===")
    
    pm = ProxyManager()
    
    # 获取一个代理并让它失败多次
    proxy = pm.get_healthy_proxy()
    if proxy:
        proxy_key = proxy.get('http')
        print(f"测试代理: {proxy_key}")
        
        # 让代理失败多次直到进入冷却期
        for i in range(5):
            pm.record_proxy_result(proxy, False)
            stats = pm.get_proxy_stats()
            print(f"失败 {i+1} 次后统计: {stats}")
        
        # 尝试再次获取该代理（应该被排除）
        new_proxy = pm.get_healthy_proxy()
        if new_proxy and new_proxy.get('http') == proxy_key:
            print("错误: 失败的代理仍然被选中")
        else:
            print("正确: 失败的代理已被排除")
        
        # 重置代理健康状态
        print("\n重置代理健康状态...")
        pm.reset_proxy_health(proxy_key)
        
        # 再次尝试获取代理
        recovered_proxy = pm.get_healthy_proxy()
        if recovered_proxy and recovered_proxy.get('http') == proxy_key:
            print("正确: 代理已恢复可用")
        else:
            print("代理恢复测试: 获取到其他代理")


if __name__ == "__main__":
    print("代理优化测试开始...")
    
    try:
        # 测试代理管理器
        pm = test_proxy_manager()
        
        # 测试爬虫代理优化
        crawler = test_crawler_with_optimized_proxy()
        
        # 模拟高负载场景
        simulate_high_load_scenario()
        
        # 测试代理恢复
        test_proxy_recovery()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
