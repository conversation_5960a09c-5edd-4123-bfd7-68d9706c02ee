#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API调用
用于诊断为什么搜索没有返回结果
"""

import time
from api_client import VAApiClient


def test_direct_api_call():
    """直接测试API调用"""
    print("🔧 直接测试Virgin Australia API调用")
    print("=" * 50)
    
    # 创建API客户端
    client = VAApiClient()
    client.use_proxy = False  # 使用直连
    
    # 测试参数
    origin = "SEA"
    destination = "HND"
    date = "2025-06-04"  # 已转换的格式
    cabin = "Business"
    
    print(f"搜索参数:")
    print(f"  出发地: {origin}")
    print(f"  目的地: {destination}")
    print(f"  日期: {date}")
    print(f"  舱位: {cabin}")
    
    # 准备认证数据和cookies
    auth_data = {}  # 空的认证数据，仅用reese84
    explicit_cookies = {
        "reese84": "test_reese84_token_12345"  # 模拟reese84令牌
    }
    
    print(f"\n认证信息:")
    print(f"  Token: {'无' if not auth_data.get('token') else '有'}")
    print(f"  ExecutionID: {'无' if not auth_data.get('execution_id') else '有'}")
    print(f"  Reese84: {'有' if explicit_cookies.get('reese84') else '无'}")
    
    print(f"\n开始API调用...")
    print("-" * 30)
    
    try:
        start_time = time.time()
        
        # 直接调用search_flights方法
        response = client.search_flights(
            origin=origin,
            destination=destination,
            date=date,
            cabin=cabin,
            auth_data=auth_data,
            explicit_cookies=explicit_cookies,
            use_specific_proxy={}  # 直连
        )
        
        response_time = time.time() - start_time
        print(f"API调用完成 (耗时: {response_time:.2f}s)")
        print("-" * 30)
        
        # 分析响应
        print(f"响应类型: {type(response)}")
        
        if response is None:
            print("❌ 响应为None")
            return False
        
        if isinstance(response, dict):
            print(f"响应键: {list(response.keys())}")
            
            # 检查错误
            if response.get("errors"):
                print("❌ API返回错误:")
                for i, error in enumerate(response["errors"]):
                    error_msg = error.get("message", "未知错误")
                    print(f"  错误 {i+1}: {error_msg}")
                return False
            
            # 检查数据
            if response.get("data"):
                print("✅ API返回数据:")
                data = response["data"]
                print(f"  数据键: {list(data.keys())}")
                
                # 检查bookingAirSearch
                booking_air_search = data.get("bookingAirSearch")
                if booking_air_search:
                    print(f"  bookingAirSearch键: {list(booking_air_search.keys())}")
                    
                    # 检查originalResponse
                    original_response = booking_air_search.get("originalResponse")
                    if original_response:
                        print(f"  originalResponse键: {list(original_response.keys())}")
                        
                        # 检查unbundledOffers
                        unbundled_offers = original_response.get("unbundledOffers")
                        if unbundled_offers:
                            print(f"  unbundledOffers类型: {type(unbundled_offers)}")
                            if isinstance(unbundled_offers, list) and unbundled_offers:
                                offers = unbundled_offers[0]
                                print(f"  航班选项数量: {len(offers) if isinstance(offers, list) else '非列表'}")
                                
                                if isinstance(offers, list) and offers:
                                    print("✅ 找到航班数据!")
                                    
                                    # 显示前几个航班
                                    for i, offer in enumerate(offers[:3]):
                                        print(f"\n  --- 航班 {i+1} ---")
                                        
                                        # 获取航班信息
                                        itinerary_part = offer.get("itineraryPart", [{}])[0]
                                        segments = itinerary_part.get("segments", [])
                                        
                                        for seg_idx, seg in enumerate(segments):
                                            flight = seg.get("flight", {})
                                            airline = flight.get("airlineCode", "")
                                            flight_no = flight.get("flightNumber", "")
                                            cabin_class = seg.get("cabinClass", "")
                                            origin_seg = seg.get("origin", "")
                                            dest_seg = seg.get("destination", "")
                                            
                                            print(f"    航段 {seg_idx+1}: {airline}{flight_no} ({origin_seg}→{dest_seg}) {cabin_class}")
                                        
                                        hash_code = offer.get("shoppingBasketHashCode")
                                        print(f"    Hash: {hash_code}")
                                    
                                    return True
                                else:
                                    print("❌ 航班选项为空")
                            else:
                                print("❌ unbundledOffers[0]不是列表或为空")
                        else:
                            print("❌ 没有unbundledOffers")
                    else:
                        print("❌ 没有originalResponse")
                else:
                    print("❌ 没有bookingAirSearch")
            else:
                print("❌ 响应中没有data字段")
        else:
            print(f"❌ 响应不是字典格式: {response}")
        
        return False
        
    except Exception as e:
        print(f"❌ API调用异常: {type(e).__name__} - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_with_different_routes():
    """测试不同的航线"""
    print("\n🌍 测试不同航线")
    print("=" * 50)
    
    routes = [
        ("SYD", "MEL", "2025-01-15"),  # 澳洲国内
        ("LAX", "SYD", "2025-02-01"),  # 国际航线
        ("SEA", "HND", "2025-06-04"),  # 原始测试航线
    ]
    
    client = VAApiClient()
    client.use_proxy = False
    
    for origin, destination, date in routes:
        print(f"\n测试航线: {origin} → {destination} ({date})")
        print("-" * 30)
        
        try:
            response = client.search_flights(
                origin=origin,
                destination=destination,
                date=date,
                cabin="Business",
                auth_data={},
                explicit_cookies={"reese84": "test_token"},
                use_specific_proxy={}
            )
            
            if response and isinstance(response, dict):
                if response.get("errors"):
                    error_msg = response["errors"][0].get("message", "未知错误")
                    print(f"  ❌ 错误: {error_msg}")
                elif response.get("data"):
                    offers_data = response.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
                    unbundled_offers = offers_data.get("unbundledOffers", [])
                    if unbundled_offers and unbundled_offers[0]:
                        offer_count = len(unbundled_offers[0])
                        print(f"  ✅ 找到 {offer_count} 个航班选项")
                    else:
                        print(f"  ⚠️  API成功但无航班数据")
                else:
                    print(f"  ❌ 无数据")
            else:
                print(f"  ❌ 无效响应")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")


def main():
    """主函数"""
    print("🔍 Virgin Australia API 直接调用测试")
    print("用于诊断搜索无结果问题")
    print("=" * 60)
    
    # 测试直接API调用
    success = test_direct_api_call()
    
    if success:
        print("\n🎉 API调用成功，找到了航班数据!")
        print("问题可能在高并发爬虫的结果处理逻辑中。")
    else:
        print("\n⚠️  API调用没有返回预期的航班数据")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. API端点变更")
        print("3. 认证信息不足")
        print("4. 查询参数问题")
        print("5. 服务器限制")
    
    # 测试不同航线
    test_with_different_routes()
    
    print("\n" + "=" * 60)
    print("📋 诊断建议:")
    print("1. 检查网络连接和防火墙设置")
    print("2. 确认API端点是否可访问")
    print("3. 获取有效的认证Token和ExecutionID")
    print("4. 验证reese84令牌生成是否正常")
    print("5. 检查Virgin Australia网站是否有变更")


if __name__ == "__main__":
    main()
