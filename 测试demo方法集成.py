#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试demo.py方法集成到高并发爬虫
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'bestV8'))

from high_concurrency_crawler import search_flights_with_demo


def test_demo_search_method():
    """测试demo.py的搜索方法"""
    print("🧪 测试demo.py搜索方法集成")
    print("=" * 50)
    
    # 测试参数
    origin = "SEA"
    destination = "HND"
    date = "2025-06-04"  # 正确的API格式
    cabin = "Business"
    
    print(f"搜索参数:")
    print(f"  出发地: {origin}")
    print(f"  目的地: {destination}")
    print(f"  日期: {date}")
    print(f"  舱位: {cabin}")
    print(f"  代理: 直连")
    
    print(f"\n开始搜索...")
    print("-" * 30)
    
    try:
        # 调用集成的demo搜索方法
        result = search_flights_with_demo(
            origin=origin,
            destination=destination,
            date=date,
            cabin=cabin,
            proxy_dict=None  # 直连
        )
        
        print(f"搜索完成!")
        print(f"结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"结果键: {list(result.keys())}")
            
            # 检查错误
            if result.get("errors"):
                print("❌ 搜索失败:")
                for i, error in enumerate(result["errors"]):
                    error_msg = error.get("message", "未知错误")
                    print(f"  错误 {i+1}: {error_msg}")
                return False
            
            # 检查数据
            elif result.get("data"):
                print("✅ 搜索成功:")
                data = result["data"]
                print(f"  数据键: {list(data.keys())}")
                
                # 检查航班数据
                booking_air_search = data.get("bookingAirSearch", {})
                if booking_air_search:
                    original_response = booking_air_search.get("originalResponse", {})
                    if original_response:
                        unbundled_offers = original_response.get("unbundledOffers", [])
                        if unbundled_offers and unbundled_offers[0]:
                            offers = unbundled_offers[0]
                            print(f"  找到 {len(offers)} 个航班选项")
                            
                            # 显示前几个航班
                            for i, offer in enumerate(offers[:3]):
                                print(f"\n  --- 航班 {i+1} ---")
                                
                                itinerary_part = offer.get("itineraryPart", [{}])[0]
                                segments = itinerary_part.get("segments", [])
                                
                                for seg_idx, seg in enumerate(segments):
                                    flight = seg.get("flight", {})
                                    airline = flight.get("airlineCode", "")
                                    flight_no = flight.get("flightNumber", "")
                                    cabin_class = seg.get("cabinClass", "")
                                    origin_seg = seg.get("origin", "")
                                    dest_seg = seg.get("destination", "")
                                    
                                    print(f"    航段 {seg_idx+1}: {airline}{flight_no} ({origin_seg}→{dest_seg}) {cabin_class}")
                                
                                hash_code = offer.get("shoppingBasketHashCode")
                                print(f"    Hash: {hash_code}")
                            
                            # 检查是否有NH117航班
                            target_flight = "NH117"
                            found_target = False
                            
                            for offer in offers:
                                itinerary_part = offer.get("itineraryPart", [{}])[0]
                                segments = itinerary_part.get("segments", [])
                                
                                for seg in segments:
                                    flight = seg.get("flight", {})
                                    airline = flight.get("airlineCode", "")
                                    flight_no = flight.get("flightNumber", "")
                                    
                                    if airline and flight_no:
                                        full_flight_no = f"{airline}{flight_no}"
                                        if full_flight_no == target_flight:
                                            found_target = True
                                            hash_code = offer.get("shoppingBasketHashCode")
                                            cabin_class = seg.get("cabinClass", "")
                                            print(f"\n  🎉 找到目标航班: {target_flight} ({cabin_class}) Hash: {hash_code}")
                                            break
                                
                                if found_target:
                                    break
                            
                            if not found_target:
                                print(f"\n  ⚠️  未找到目标航班: {target_flight}")
                            
                            return True
                        else:
                            print("  ❌ 无航班选项")
                    else:
                        print("  ❌ 无originalResponse")
                else:
                    print("  ❌ 无bookingAirSearch")
            else:
                print("❌ 无数据")
        else:
            print(f"❌ 无效响应格式: {result}")
        
        return False
        
    except Exception as e:
        print(f"❌ 搜索异常: {type(e).__name__} - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_routes():
    """测试不同航线"""
    print("\n🌍 测试不同航线")
    print("=" * 50)
    
    routes = [
        ("SYD", "MEL", "2025-01-15", "Business"),  # 澳洲国内
        ("LAX", "SYD", "2025-02-01", "Business"),  # 国际航线
        ("SEA", "HND", "2025-06-04", "Business"),  # 原始测试航线
    ]
    
    for origin, destination, date, cabin in routes:
        print(f"\n测试航线: {origin} → {destination} ({date}) {cabin}")
        print("-" * 30)
        
        try:
            result = search_flights_with_demo(
                origin=origin,
                destination=destination,
                date=date,
                cabin=cabin,
                proxy_dict=None
            )
            
            if result and isinstance(result, dict):
                if result.get("errors"):
                    error_msg = result["errors"][0].get("message", "未知错误")
                    print(f"  ❌ 错误: {error_msg}")
                elif result.get("data"):
                    offers_data = result.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
                    unbundled_offers = offers_data.get("unbundledOffers", [])
                    if unbundled_offers and unbundled_offers[0]:
                        offer_count = len(unbundled_offers[0])
                        print(f"  ✅ 找到 {offer_count} 个航班选项")
                    else:
                        print(f"  ⚠️  API成功但无航班数据")
                else:
                    print(f"  ❌ 无数据")
            else:
                print(f"  ❌ 无效响应")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")


def main():
    """主函数"""
    print("🔧 Demo.py方法集成测试")
    print("验证高并发爬虫使用demo.py方法进行搜索")
    print("=" * 60)
    
    # 测试demo搜索方法
    success = test_demo_search_method()
    
    if success:
        print("\n🎉 Demo.py方法集成成功!")
        print("高并发爬虫现在可以使用demo.py的方法进行搜索了。")
    else:
        print("\n⚠️  Demo.py方法集成测试未完全成功")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. Reese84生成失败")
        print("3. API端点问题")
        print("4. 查询参数问题")
    
    # 测试不同航线
    test_different_routes()
    
    print("\n" + "=" * 60)
    print("📋 集成说明:")
    print("1. ✅ 高并发爬虫现在使用demo.py的search_flights_with_demo方法")
    print("2. ✅ 不再需要Token和ExecutionID进行高频搜索")
    print("3. ✅ Reese84生成已集成到搜索方法中")
    print("4. ✅ 支持代理切换和错误处理")
    print("5. ✅ 保持与原有匹配逻辑的兼容性")
    
    print("\n🚀 使用方法:")
    print("- 高频搜索阶段：使用demo.py方法，无需认证")
    print("- 找到航班后：使用直连方式进行二次搜索和预订")
    print("- 程序架构：UI → 高频搜索(demo.py) → 找到航班 → 直连预订")


if __name__ == "__main__":
    main()
