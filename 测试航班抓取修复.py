#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试航班抓取修复效果
验证日期格式转换和搜索结果显示
"""

import time
import threading
from datetime import datetime
from api_client import VAApiClient


def test_date_conversion():
    """测试日期格式转换"""
    print("🧪 测试日期格式转换")
    print("-" * 40)
    
    test_cases = [
        "20250604",  # 用户实际输入
        "20241225",  # 圣诞节
        "20250101",  # 新年
    ]
    
    for test_date in test_cases:
        try:
            dt_obj = datetime.strptime(test_date, "%Y%m%d")
            api_date_format = dt_obj.strftime("%Y-%m-%d")
            print(f"✅ {test_date} → {api_date_format}")
        except ValueError as e:
            print(f"❌ {test_date} 转换失败: {e}")


def test_api_search():
    """测试API搜索"""
    print("\n🔍 测试API搜索")
    print("-" * 40)
    
    # 创建API客户端
    client = VAApiClient()
    client.use_proxy = False  # 使用直连测试
    
    # 测试参数
    origin = "SEA"
    destination = "HND"
    date_raw = "20250604"
    
    # 转换日期格式
    try:
        dt_obj = datetime.strptime(date_raw, "%Y%m%d")
        api_date = dt_obj.strftime("%Y-%m-%d")
        print(f"日期转换: {date_raw} → {api_date}")
    except ValueError as e:
        print(f"❌ 日期转换失败: {e}")
        return
    
    # 准备搜索参数
    auth_data = {}  # 空的认证数据
    explicit_cookies = {
        "reese84": "test_token_12345"  # 模拟reese84令牌
    }
    
    print(f"搜索参数: {origin} → {destination}, {api_date}")
    print("开始搜索...")
    
    try:
        start_time = time.time()
        response = client.search_flights(
            origin=origin,
            destination=destination,
            date=api_date,
            cabin="Business",
            auth_data=auth_data,
            explicit_cookies=explicit_cookies,
            use_specific_proxy={}  # 直连
        )
        response_time = time.time() - start_time
        
        print(f"搜索完成 (耗时: {response_time:.1f}s)")
        
        # 分析响应
        if isinstance(response, dict):
            if response.get("errors"):
                error_msg = str(response['errors'])[:100]
                print(f"❌ 搜索失败: {error_msg}...")
            elif response.get("data"):
                # 检查是否有航班数据
                offers_data = response.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
                unbundled_offers = offers_data.get("unbundledOffers", [])
                if unbundled_offers and unbundled_offers[0]:
                    offer_count = len(unbundled_offers[0])
                    print(f"✅ 搜索成功: 找到 {offer_count} 个航班选项")
                    
                    # 检查是否有NH117航班
                    target_flight = "NH117"
                    found_target = False
                    
                    for i, offer in enumerate(unbundled_offers[0][:3]):  # 只检查前3个
                        itinerary_part = offer.get("itineraryPart", [{}])[0]
                        segments = itinerary_part.get("segments", [])
                        
                        for seg in segments:
                            flight = seg.get("flight", {})
                            flight_no = flight.get("flightNumber")
                            airline = flight.get("airlineCode")
                            cabin_class = seg.get("cabinClass", "")
                            
                            if flight_no and airline:
                                full_flight_no = f"{airline}{flight_no}"
                                print(f"  航班 {i+1}: {full_flight_no} ({cabin_class})")
                                
                                if full_flight_no == target_flight:
                                    found_target = True
                                    hash_code = offer.get("shoppingBasketHashCode")
                                    print(f"  🎉 找到目标航班: {target_flight} (Hash: {hash_code})")
                    
                    if not found_target:
                        print(f"  ⚠️  未找到目标航班: {target_flight}")
                else:
                    print("⚠️  搜索成功但无航班数据")
            else:
                print("⚠️  搜索返回空数据")
        else:
            print("❌ 搜索返回异常格式")
            
    except Exception as e:
        print(f"❌ 搜索请求失败: {e}")


def test_flight_matching():
    """测试航班匹配逻辑"""
    print("\n✈️  测试航班匹配逻辑")
    print("-" * 40)
    
    # 模拟搜索结果
    mock_offers = [
        {
            "shoppingBasketHashCode": "-628943271",
            "itineraryPart": [
                {
                    "segments": [
                        {
                            "flight": {
                                "airlineCode": "NH",
                                "flightNumber": "117"
                            },
                            "cabinClass": "Business"
                        }
                    ]
                }
            ]
        },
        {
            "shoppingBasketHashCode": "-123456789",
            "itineraryPart": [
                {
                    "segments": [
                        {
                            "flight": {
                                "airlineCode": "NH",
                                "flightNumber": "118"
                            },
                            "cabinClass": "Business"
                        }
                    ]
                }
            ]
        }
    ]
    
    target_flights = ["NH117"]
    target_cabin = "Business"
    
    print(f"目标航班: {target_flights}")
    print(f"目标舱位: {target_cabin}")
    
    for i, offer in enumerate(mock_offers):
        print(f"\n--- 检查航班选项 {i+1} ---")
        
        itinerary_part = offer.get("itineraryPart", [{}])[0]
        segments = itinerary_part.get("segments", [])
        
        offer_flight_nos = []
        offer_cabins = []
        
        for seg in segments:
            flight = seg.get("flight", {})
            flight_no = flight.get("flightNumber")
            airline = flight.get("airlineCode")
            cabin_class = seg.get("cabinClass", "")
            
            if flight_no and airline:
                full_flight_no = f"{airline}{flight_no}"
                offer_flight_nos.append(full_flight_no)
                print(f"  航班: {full_flight_no}")
            
            offer_cabins.append(cabin_class)
            print(f"  舱位: {cabin_class}")
        
        # 检查匹配
        flight_match = offer_flight_nos == target_flights
        cabin_match = all(cabin.lower() == target_cabin.lower() for cabin in offer_cabins if cabin)
        
        print(f"  航班匹配: {'✅' if flight_match else '❌'}")
        print(f"  舱位匹配: {'✅' if cabin_match else '❌'}")
        
        if flight_match and cabin_match:
            hash_code = offer.get("shoppingBasketHashCode")
            print(f"  🎉 完全匹配! Hash: {hash_code}")


def main():
    """主测试函数"""
    print("🔧 航班抓取修复测试")
    print("=" * 50)
    
    # 运行测试
    test_date_conversion()
    test_api_search()
    test_flight_matching()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print("1. ✅ 日期格式转换正常")
    print("2. 🔍 API搜索测试完成")
    print("3. ✈️  航班匹配逻辑验证")
    
    print("\n💡 修复说明:")
    print("- 用户输入20250604会被转换为2025-06-04")
    print("- API调用使用正确的日期格式")
    print("- 搜索结果会显示清晰的成功/失败信息")
    print("- 航班匹配逻辑保持不变")
    
    print("\n🎯 现在程序应该能够:")
    print("- 正确转换日期格式")
    print("- 成功调用搜索API")
    print("- 清晰显示搜索结果")
    print("- 找到匹配的航班（如果存在）")


if __name__ == "__main__":
    main()
