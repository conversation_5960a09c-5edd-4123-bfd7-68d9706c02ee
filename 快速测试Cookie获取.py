#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试Cookie获取修复效果
"""

import asyncio
import websockets
import json
from datetime import datetime


async def test_cookie_fix():
    """测试Cookie获取修复"""
    print("🧪 快速测试Cookie获取修复")
    print("="*50)
    
    received_messages = []
    cookie_received = False
    
    async def handle_message(websocket, path):
        nonlocal cookie_received
        print(f"🔗 扩展已连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                data = json.loads(message)
                message_type = data.get("type", "unknown")
                timestamp = datetime.now().strftime("%H:%M:%S")
                
                print(f"[{timestamp}] 📨 {message_type}")
                received_messages.append({"time": timestamp, "type": message_type, "data": data})
                
                if message_type == "cookie_data":
                    cookie_received = True
                    payload = data.get("payload", [])
                    execution_id = data.get("execution_id", "")
                    print(f"🍪 Cookie数据: {len(payload)} 个")
                    print(f"📋 ExecutionID: {'有' if execution_id else '无'}")
                    
                    # 检查重要Cookie
                    important = [c for c in payload if isinstance(c, dict) and c.get("name") in ["DCSESSIONID", "IBEOpenToken", "reese84"]]
                    print(f"🎯 重要Cookie: {len(important)} 个")
                    
                    await websocket.send(json.dumps({
                        "status": "success",
                        "message": "Cookie数据已收到并已开始调度处理"
                    }))
                
                elif message_type == "status_update":
                    message_text = data.get("message", "")
                    error_text = data.get("error", "")
                    
                    if error_text:
                        print(f"❌ 错误: {error_text}")
                    else:
                        print(f"ℹ️  状态: {message_text}")
                    
                    await websocket.send(json.dumps({
                        "status": "received",
                        "message": "状态更新已收到"
                    }))
        
        except websockets.exceptions.ConnectionClosed:
            print(f"📱 扩展断开连接")
        except Exception as e:
            print(f"❌ 处理消息出错: {e}")
    
    try:
        print(f"🚀 启动测试服务器 (端口: 8765)...")
        async with websockets.serve(handle_message, "127.0.0.1", 8765):
            print(f"✅ 服务器运行中")
            print(f"📋 请按以下步骤测试:")
            print(f"1. 打开Firefox浏览器")
            print(f"2. 访问Virgin Australia网站并登录")
            print(f"3. 观察扩展是否自动连接和获取Cookie")
            print(f"4. 或者手动点击扩展图标获取Cookie")
            print(f"="*50)
            
            # 等待30秒观察结果
            await asyncio.sleep(30)
            
            print(f"\n📊 测试结果:")
            print(f"总消息数: {len(received_messages)}")
            print(f"Cookie获取: {'成功' if cookie_received else '失败'}")
            
            if received_messages:
                print(f"\n📝 消息记录:")
                for msg in received_messages[-5:]:  # 显示最后5条消息
                    print(f"  [{msg['time']}] {msg['type']}")
            
            if cookie_received:
                print(f"\n🎉 修复成功! Cookie获取功能正常")
            else:
                print(f"\n⚠️  Cookie获取可能仍有问题")
                print(f"💡 建议:")
                print(f"1. 重新加载浏览器扩展")
                print(f"2. 检查浏览器控制台错误")
                print(f"3. 确认网站已登录")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_cookie_fix())
