import time
import threading
import queue
import concurrent.futures
from datetime import datetime
import json
import os
import sys
import signal
import copy
from api_client import VAApiClient
import random
from bestV8.demo import Reese84Resolve
from proxy_manager import get_proxy_manager

# Removed direct import from ui.py to break circular dependency
# from ui import RedeemApp, get_redis_client
# REDIS_AVAILABLE = False
# try:
#    from ui import RedeemApp, get_redis_client
#    REDIS_AVAILABLE = True
# except ImportError as e:
#    print(f"[抓取器 WARNING] 无法从 ui.py 导入 RedeemApp 或 get_redis_client: {e}. Redis功能将受限或不可用。")
#    def get_redis_client(): return None
#    class RedeemApp:
#        @staticmethod
#        def get_instance(): return None

AKAMAI_CHALLENGE_URL_FOR_REESE84_CRAWLER = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'

class HighConcurrencyCrawler:
    """高并发航班抓取器，支持50个并发任务，任务报错立即重试，每次请求使用随机IP"""

    def __init__(self, max_workers=50, max_retries=3, app_instance_ref=None):
        """
        初始化高并发抓取器
        :param max_workers: 最大并发任务数，默认50
        :param max_retries: 单个任务最大重试次数，默认3
        """
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.app_instance_ref = app_instance_ref # Store reference to RedeemApp instance
        self.running = False
        self.targets = []  # 抓取目标列表 (will be singular for a task-specific crawler)
        self.api_client_config_template = None # Will store the VAApiClient used for config
        self.task_id_for_redis = None # Task ID to fetch cookies from Redis
        self.executor = None  # 线程池执行器
        self.callback = None  # 找到匹配航班时的回调函数
        self.debug_mode = True # Or get from active_api_client if it has such a setting
        self.debug_dir = "debug_logs" # Should be configured externally or made instance specific
        self.found_match = False  # 是否找到匹配航班
        self.lock = threading.Lock()  # 用于线程安全操作
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "retried_requests": 0,
            "start_time": None,
            "end_time": None
        }
        self.worker_thread = None # Initialize worker_thread attribute
        self.stats_thread = None # Added initialization for stats_thread

        # 使用全局代理管理器
        self.proxy_manager = get_proxy_manager()

        # 创建调试目录 - consider if this should be done by the main app
        if self.debug_mode and not os.path.exists(self.debug_dir):
            try:
                os.makedirs(self.debug_dir)
            except OSError as e:
                self._safe_print(f"[抓取器] 创建调试目录失败: {e} (可能已被其他实例创建)")

    def _safe_print(self, message):
        """安全的打印方法，避免Windows控制台编码问题"""
        try:
            print(message)
        except (OSError, UnicodeEncodeError, UnicodeDecodeError) as e:
            try:
                # 尝试使用ASCII编码
                print(message.encode('ascii', 'ignore').decode('ascii'))
            except:
                try:
                    # 最后尝试简化消息
                    print(f"[LOG] {len(message)} chars message (encoding error)")
                except:
                    pass  # 完全静默失败

    def add_target(self, origin, destination, date, flight_nos_str):
        """
        添加抓取目标 (For a task-specific crawler, this will likely be one target)
        :param origin: 出发地
        :param destination: 目的地
        :param date: 日期
        :param flight_nos_str: 航班号字符串，多个航班号用+分隔
        """
        target = {
            "origin": origin,
            "destination": destination,
            "date": date,
            "flight_nos": flight_nos_str.upper().split('+')
        }
        self.targets.append(target) # Should ideally only be one for a task's crawler
        print(f"[抓取器] 添加目标: {origin}-{destination} {date} 航班: {'+'.join(target['flight_nos'])}")
        return len(self.targets) - 1

    def set_callback(self, callback_function):
        """设置找到目标航班时的回调函数"""
        self.callback = callback_function

    def _get_healthy_proxy(self, exclude_proxy=None):
        """获取一个健康的代理"""
        if not (self.api_client_config_template and
                self.api_client_config_template.use_proxy and
                self.api_client_config_template.proxies):
            return {}

        exclude_proxies = [exclude_proxy] if exclude_proxy else []
        proxy = self.proxy_manager.get_healthy_proxy(exclude_proxies=exclude_proxies)
        return proxy or {}

    def _record_proxy_result(self, proxy, success, response_time=None):
        """记录代理使用结果"""
        if proxy:
            self.proxy_manager.record_proxy_result(proxy, success, response_time)

    def start(self, task_id_for_redis=None, initial_api_client_template=None):
        """
        开始高并发抓取
        :param task_id_for_redis: The task ID to fetch cookies from Redis
        :param initial_api_client_template: The VAApiClient instance for this task.
        """
        if self.running:
            print(f"[HCC TaskMapID:{self.task_id_for_redis or 'N/A'}] 已在运行")
            return False

        if not self.targets:
            print(f"[HCC TaskMapID:{self.task_id_for_redis or 'N/A'}] 错误: 无抓取目标")
            return False

        if task_id_for_redis is None:
            print(f"[HCC TaskMapID:N/A] 错误: 需提供 task_id_for_redis")
            return False

        if not self.app_instance_ref:
            print(f"[HCC TaskMapID:{task_id_for_redis}] 错误: app_instance_ref 未在构造时提供。无法启动爬虫。")
            return False

        self.task_id_for_redis = task_id_for_redis
        self.api_client_config_template = initial_api_client_template if initial_api_client_template else VAApiClient()
        self.found_match = False
        self.stats = {k: (0 if isinstance(v, int) else None) for k, v in self.stats.items()}
        self.stats["start_time"] = datetime.now()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        self.stats_thread = threading.Thread(target=self._stats_loop, daemon=True)
        self.stats_thread.start()
        self._safe_print(f"[HCC TaskMapID:{self.task_id_for_redis}] 开始抓取 (并发: {self.max_workers})")
        return True

    def stop(self):
        """停止抓取"""
        if not self.running:
            # print("[抓取器] 抓取器未在运行") # Can be noisy if called multiple times
            return

        self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 正在停止抓取器...")
        self.running = False # Signal threads to stop

        # 关闭线程池 - use try-except as executor might be None if start failed early
        try:
            if self.executor:
                # 首先尝试取消所有未开始的任务
                try:
                    self.executor.shutdown(wait=False, cancel_futures=True) # Python 3.9+
                    self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 线程池已关闭，等待任务完成...")
                    # 给一些时间让正在运行的任务检查self.running状态并退出
                    time.sleep(2)
                except TypeError:
                    # Python < 3.9 不支持 cancel_futures 参数
                    self.executor.shutdown(wait=False)
                    self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 线程池已关闭（旧版Python）...")
                    time.sleep(2)
        except Exception as e_shutdown:
            self._safe_print(f"[抓取器] 关闭线程池时发生错误: {e_shutdown}")


        # 等待工作线程结束 (worker_thread might not have started if start failed early)
        if self.worker_thread and self.worker_thread.is_alive():
            try:
                self.worker_thread.join(timeout=5)
                if self.worker_thread.is_alive():
                    self._safe_print("[抓取器] 警告: 工作线程未在5秒内结束。")
            except Exception as e_join_worker:
                 self._safe_print(f"[抓取器] 等待工作线程结束时出错: {e_join_worker}")


        # 等待统计线程结束
        if hasattr(self, 'stats_thread') and self.stats_thread.is_alive():
            try:
                self.stats_thread.join(timeout=2)
            except Exception as e_join_stats:
                self._safe_print(f"[抓取器] 等待统计线程结束时出错: {e_join_stats}")


        # 记录结束时间
        self.stats["end_time"] = datetime.now()

        # 打印统计信息
        self._print_final_stats()

        self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 已停止抓取器")

    def _worker_loop(self):
        """工作线程循环，负责提交任务到线程池"""
        futures = []
        if not self.targets: self.running = False; self._safe_print(f"[HCC TaskMapID:{self.task_id_for_redis}] Worker: 无目标。"); return
        target_info = self.targets[0]; target_idx = 0
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis}]"
        while self.running and not self.found_match:
            # 更频繁地检查停止状态
            if not self.running or self.found_match:
                self._safe_print(f"{log_prefix} 工作循环检测到停止信号，退出")
                break

            active_futures_count = sum(1 for f in futures if not f.done())
            num_to_submit = self.max_workers - active_futures_count
            for _ in range(num_to_submit):
                if not self.running or self.found_match: break
                future = self.executor.submit(self._search_task, target_idx, target_info["origin"], target_info["destination"], target_info["date"], target_info["flight_nos"])
                futures.append(future)
            temp_futures = []
            for f_idx, f_val in enumerate(futures):
                if f_val.done():
                    try: f_val.result()
                    except Exception as e_f: pass # print(f"{log_prefix} Future {f_idx} 结果异常: {e_f}")
                else: temp_futures.append(f_val)
            futures = temp_futures

            # 使用更短的休息时间，并在休息期间检查停止状态
            for _ in range(10):  # 0.1秒总休息时间，分成10个0.01秒检查
                if not self.running or self.found_match:
                    break
                time.sleep(0.01)
        # 取消所有剩余的futures
        for future in futures:
            if not future.done():
                future.cancel()

        self._safe_print(f"{log_prefix} 工作线程结束，已取消 {len([f for f in futures if not f.done()])} 个未完成任务")
        self.running = False

    def _search_task(self, target_idx, origin, destination, date, target_flight_nos, retry_count=0):
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis}]"
        if self.found_match or not self.running: return

        # 优化代理策略：为reese84生成和API调用分别选择健康代理
        reese84_proxy_dict = self._get_healthy_proxy() # reese84生成使用的代理
        api_proxy_dict = self._get_healthy_proxy(exclude_proxy=reese84_proxy_dict) # API调用使用不同的代理

        # 如果没有足够的代理，API调用使用直连
        if not api_proxy_dict and reese84_proxy_dict:
            api_proxy_dict = {}  # 直连

        self._safe_print(f"{log_prefix} (Tgt {target_idx}) Attempt {retry_count + 1}: Reese84代理: {reese84_proxy_dict.get('http', 'Direct')}, API代理: {api_proxy_dict.get('http', 'Direct')}")

        # 再次检查运行状态
        if self.found_match or not self.running: return

        # 2. Generate reese84 token using the selected proxy strategy
        reese84_value = None
        ua_from_reese = None # User-Agent potentially returned by Reese84Resolve
        if self.app_instance_ref and hasattr(self.app_instance_ref, '_generate_reese84_for_task'):
            # Expecting (token, ua, error_message_or_none) from _generate_reese84_for_task
            reese_result = self.app_instance_ref._generate_reese84_for_task(
                self.task_id_for_redis,
                AKAMAI_CHALLENGE_URL_FOR_REESE84_CRAWLER,
                proxy_to_use_for_reese=reese84_proxy_dict # 使用专门为reese84选择的代理
            )
            reese84_value, ua_from_reese, reese_error_detail = None, None, "Initial error state for reese detail."

            if isinstance(reese_result, tuple):
                if len(reese_result) == 3:
                    reese84_value, ua_from_reese, reese_error_detail = reese_result
                    if reese84_value is not None and reese_error_detail is None:
                        reese_error_detail = "Reese84 generated successfully." # Success case
                    elif reese84_value is None and reese_error_detail is None:
                        reese_error_detail = "Reese84 generation failed, no specific error message provided by generator."
                elif len(reese_result) == 2: # Handle legacy 2-tuple return for now
                    reese84_value, ua_from_reese = reese_result
                    if reese84_value is None:
                        reese_error_detail = "Reese84 generation failed (Task's _generate_reese84_for_task returned 2 values; update for detailed errors)."
                    else:
                        reese_error_detail = "Reese84 generated successfully (Task's _generate_reese84_for_task returned 2 values)."
                else:
                    reese_error_detail = f"Reese84 generation: _generate_reese84_for_task returned unexpected tuple size: {len(reese_result)}."
            else:
                reese_error_detail = f"Reese84 generation: _generate_reese84_for_task returned unexpected type: {type(reese_result)}."

        if not reese84_value:
            # 记录reese84代理失败
            self._record_proxy_result(reese84_proxy_dict, False)

            if hasattr(self.app_instance_ref, 'get_task_by_id'):
                task = self.app_instance_ref.get_task_by_id(self.task_id_for_redis)
                if task: task.log_task_message(f"Reese84 gen failed (attempt {retry_count + 1}): {reese_error_detail}", "yellow")
            # Include reese_error_detail in the exception message
            raise Exception(f"Reese84 generation failed: {reese_error_detail}. Reese84代理: {reese84_proxy_dict.get('http') if reese84_proxy_dict else 'None'}. Triggering retry for _search_task")
        else:
            # 记录reese84代理成功
            self._record_proxy_result(reese84_proxy_dict, True)

        # 再次检查运行状态（reese84生成后）
        if self.found_match or not self.running: return

        # 3. Prepare auth_data and explicit_cookies for VAApiClient.search_flights
        token_from_template = None
        exec_id_from_template = None
        ua_override_from_template = None
        session_ua_from_template = None

        if self.api_client_config_template:
            token_from_template = getattr(self.api_client_config_template, 'token', None)
            exec_id_from_template = getattr(self.api_client_config_template, 'execution_id', None)
            ua_override_from_template = getattr(self.api_client_config_template, 'user_agent_override', None)
            if hasattr(self.api_client_config_template, 'session') and self.api_client_config_template.session and hasattr(self.api_client_config_template.session, 'headers'):
                session_ua_from_template = self.api_client_config_template.session.headers.get('User-Agent')

        effective_ua = ua_from_reese or ua_override_from_template or session_ua_from_template
        if not effective_ua: # Final fallback
            effective_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"

        api_auth_data_for_search = {
            "token": token_from_template,
            "execution_id": exec_id_from_template,
            "user_agent": effective_ua
        }
        explicit_cookies_for_search = {'reese84': reese84_value}

        client_to_use = VAApiClient()
        if self.api_client_config_template:
            client_to_use.proxies = list(self.api_client_config_template.proxies) if self.api_client_config_template.proxies else []
            client_to_use.use_proxy = self.api_client_config_template.use_proxy
        else:
            client_to_use.proxies = []
            client_to_use.use_proxy = False

        # 最后一次检查运行状态（API调用前）
        if self.found_match or not self.running: return

        try:
            with self.lock: self.stats["total_requests"] += 1
            # First TEMP PRINT (We see this in logs)
            log_msg_token_status = 'present' if api_auth_data_for_search.get('token') else 'MISSING'
            log_msg_execid_status = 'present' if api_auth_data_for_search.get('execution_id') else 'MISSING'
            log_msg_reese_status = 'present' if explicit_cookies_for_search.get('reese84') else 'MISSING'
            log_message_pre_search = (
                f"[TEMP PRINT] {log_prefix} (Tgt {target_idx}) Attempt {retry_count + 1}: Intending to call search_flights. "
                f"AuthToken: {log_msg_token_status}, ExecID: {log_msg_execid_status}, reese-cookie: {log_msg_reese_status}. "
                f"API代理: {api_proxy_dict.get('http', 'Direct')}."
            )
            self._safe_print(log_message_pre_search) # THIS IS THE [TEMP PRINT] WE SEE

            search_params = {
                "origin": origin, "destination": destination, "date": date,
                "cabin": self.targets[target_idx].get("cabin", "Business"),
                "auth_data": api_auth_data_for_search,
                "explicit_cookies": explicit_cookies_for_search,
                "use_specific_proxy": api_proxy_dict # 使用为API专门选择的代理
            }

            self._safe_print(f"[HCC DEBUG] {log_prefix} (Tgt {target_idx}) Just BEFORE calling client_to_use.search_flights. search_params O/D/D: {search_params.get('origin')}/{search_params.get('destination')}/{search_params.get('date')}")

            # 记录API调用时间
            api_start_time = time.time()
            search_results = client_to_use.search_flights(**search_params)
            api_response_time = time.time() - api_start_time

            self._safe_print(f"[HCC DEBUG] {log_prefix} (Tgt {target_idx}) Just AFTER calling client_to_use.search_flights. Results received (type: {type(search_results).__name__}). Response time: {api_response_time:.2f}s")

            result_summary = 'N/A (not a dict)'
            if isinstance(search_results, dict):
                result_summary = f"Keys: {list(search_results.keys())}"
                if "errors" in search_results:
                    result_summary += f" Errors: {str(search_results['errors'])[:100]}..."
                if "data" in search_results and search_results.get("data") is not None:
                     result_summary += f" Data keys: {list(search_results.get('data', {}).keys())}"
                elif "data" in search_results and search_results.get("data") is None:
                     result_summary += f" Data is None"
                elif "data" not in search_results:
                     result_summary += f" No 'data' key"


            log_message_post_search = (
                f"[TEMP PRINT] {log_prefix} (Tgt {target_idx}) Attempt {retry_count + 1}: search_flights returned. "
                f"Type: {type(search_results)}. Summary: {result_summary}"
            )
            # if task_for_log_search: task_for_log_search.log_task_message(log_message_post_search, "blue")
            # else: print(log_message_post_search)
            self._safe_print(log_message_post_search) # TEMP USE PRINT


            # 记录API代理成功
            self._record_proxy_result(api_proxy_dict, True, api_response_time)

            with self.lock: self.stats["successful_requests"] += 1
            if self.debug_mode and random.random() < 0.05: self._save_debug_data(search_results, target_idx)

            current_target_info = self.targets[target_idx]
            found_this_call, match_info = self._check_search_results(search_results, target_flight_nos, current_target_info)
            if found_this_call:
                with self.lock:
                    if not self.found_match:
                        self.found_match = True; self.running = False
                        if match_info and isinstance(match_info, dict):
                            match_info['proxy_used_for_success'] = api_proxy_dict # 记录成功使用的API代理
                        if self.callback: self.callback(match_info, self.task_id_for_redis)
            return
        except Exception as e:
            # 记录API代理失败
            self._record_proxy_result(api_proxy_dict, False)

            with self.lock: self.stats["failed_requests"] += 1
            # Log detailed error for search_flights failure
            log_message_detail = f"(Tgt {target_idx}) Search flights attempt {retry_count + 1} FAILED. API代理: {api_proxy_dict.get('http', 'Direct')}. Error: {type(e).__name__} - {e}"
            task_for_log = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
            if task_for_log:
                task_for_log.log_task_message(log_message_detail, "red", include_traceback=True)
            else:
                self._safe_print(f"{log_prefix} {log_message_detail}")
                if self.debug_mode: # Fallback for traceback if no task logger
                    import traceback
                    traceback.print_exc()

            if retry_count < self.max_retries and self.running and not self.found_match:
                with self.lock: self.stats["retried_requests"] += 1
                time.sleep(random.uniform(1.5, 3.5 + retry_count * 0.5))
                return self._search_task(target_idx, origin, destination, date, target_flight_nos, retry_count + 1)
            return

    def _check_search_results(self, search_results, target_flight_nos, current_target_params):
        """
        检查搜索结果中是否有匹配的航班
        :param search_results: 搜索结果
        :param target_flight_nos: 目标航班号列表 (用户输入的，如 ['NH123'])
        :param current_target_params: 当前挂单目标的参数 (字典，包含 'origin', 'destination', 'date')
        :return: (是否找到匹配, 匹配信息字典 或 None)
        """
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis}]"
        try:
            if not search_results: # Basic check for empty response
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                log_msg = f"{log_prefix} 检查结果：search_flights 返回了空结果 (None or empty)."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            if search_results.get("errors") and not search_results.get("data"):
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                error_summary = str(search_results.get("errors"))[:250] # Log a summary of errors
                log_msg = f"{log_prefix} 检查结果：search_flights 返回 GraphQL 错误: {error_summary}..."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            if not search_results.get("data"): # Check for missing data key if not errors
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                log_msg = f"{log_prefix} 检查结果：search_flights 返回结果中无 'data' 键。结果: {str(search_results)[:200]}..."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            offers_data = search_results.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
            if not offers_data:
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                log_msg = f"{log_prefix} 检查结果：未在 search_results.data.bookingAirSearch 中找到 originalResponse。Data: {str(search_results.get('data'))[:200]}..."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            unbundled_offers_list = offers_data.get("unbundledOffers")
            if not unbundled_offers_list or not isinstance(unbundled_offers_list, list) or not unbundled_offers_list[0]:
                # print("[抓取器] 检查结果：未找到 unbundledOffers 或列表为空。") # Noisy
                return False, None

            offers = unbundled_offers_list[0] # This is a list of offer objects

            # 获取 cabin class to search from current_target_params if specified, else default to Business
            # This is important if user can specify cabin in the input task.
            # The search_flights in VAApiClient might already filter by a default cabin.
            # Here, we ensure the *found* offer matches what we *expect* for this task.
            expected_cabin_class = current_target_params.get("cabin", "Business").lower()


            for offer in offers:
                itinerary_part = offer.get("itineraryPart", [{}])[0]
                if not itinerary_part: continue

                segments = itinerary_part.get("segments", [])
                if not segments: continue

                offer_flight_nos_found = []
                offer_cabin_classes_found = []
                all_segments_match_expected_cabin = True

                for seg in segments:
                    flight = seg.get("flight", {})
                    flight_no = flight.get("flightNumber")
                    airline = flight.get("airlineCode")
                    if flight_no and airline:
                        offer_flight_nos_found.append(f"{airline}{flight_no}")

                    cabin_class_segment = seg.get("cabinClass", "").lower()
                    offer_cabin_classes_found.append(seg.get("cabinClass", "")) # Store original casing for signature

                    # Check if this segment's cabin matches the expected cabin for the task
                    if cabin_class_segment != expected_cabin_class:
                        all_segments_match_expected_cabin = False
                        # print(f"[抓取器 DEBUG] Offer segment cabin '{cabin_class_segment}' != expected '{expected_cabin_class}'")
                        break # This offer is not a match for the expected cabin class for all segments

                if not all_segments_match_expected_cabin:
                    continue # Move to the next offer

                # Compare flight numbers (case-insensitive for matching)
                # target_flight_nos comes from user input, could be e.g., ["VA9", "VA123"]
                # offer_flight_nos_found comes from API, e.g., ["VA009", "VA0123"]
                # We need a flexible match.

                # Normalize both lists for comparison: uppercase and remove leading zeros from flight number part
                def normalize_flight_list(fl_list):
                    normalized = []
                    for fn_full in fl_list:
                        fn_full_upper = fn_full.upper()
                        # Separate airline code (non-digits at start) from number part
                        airline_code = "".join(filter(str.isalpha, fn_full_upper))
                        number_part_str = "".join(filter(str.isdigit, fn_full_upper))
                        if number_part_str: # Convert to int to remove leading zeros, then back to str
                             normalized.append(f"{airline_code}{int(number_part_str)}")
                        else: # Should not happen for valid flight numbers
                             normalized.append(fn_full_upper)
                    return sorted(normalized)

                normalized_target_flights = normalize_flight_list(target_flight_nos)
                normalized_offer_flights = normalize_flight_list(offer_flight_nos_found)

                if normalized_target_flights == normalized_offer_flights:
                    # Flight numbers and cabin classes match expectations.

                    flight_signature_segments = []
                    for seg_detail in segments:
                        flight_data = seg_detail.get("flight", {})
                        flight_signature_segments.append({
                            "origin": seg_detail.get("origin"),
                            "destination": seg_detail.get("destination"),
                            "departure": seg_detail.get("departure"),
                            "arrival": seg_detail.get("arrival"),
                            "airline": flight_data.get("airlineCode"),
                            "flightNumber": flight_data.get("flightNumber")
                        })

                    flight_signature = {
                        "origin": current_target_params.get("origin"),
                        "destination": current_target_params.get("destination"),
                        "date": current_target_params.get("date"), # YYYY-MM-DD
                        "cabinClass": current_target_params.get("cabin", "Business"), # Cabin used for this task's search
                        "flight_numbers_queried": target_flight_nos,
                        "offer_flight_numbers_found": offer_flight_nos_found, # Actual from API
                        "offer_cabin_classes_found": offer_cabin_classes_found, # Actual from API
                        "offer_segments_details": flight_signature_segments
                    }

                    match_info = {
                        "original_offer_data": offer,
                        "original_shoppingBasketHashCode": offer.get("shoppingBasketHashCode"),
                        "flight_signature": flight_signature,
                        "found_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    }
                    self.log_green(f"[抓取器] 匹配成功! Offer Hash: {match_info['original_shoppingBasketHashCode']}")
                    self.log_green(f"    Flight Sig: O={flight_signature['origin']}, D={flight_signature['destination']}, Date={flight_signature['date']}, Cabin={flight_signature['cabinClass']}")
                    self.log_green(f"    Offer Flights: {flight_signature['offer_flight_numbers_found']}")
                    return True, match_info

            return False, None

        except Exception as e:
            self._safe_print(f"{log_prefix} Check results error: {e}")
            # import traceback
            # print(traceback.format_exc()) # For deeper debug
            return False, None

    def log_green(self, msg):
        self._safe_print(f"\033[92m{msg}\033[0m")

    def _save_debug_data(self, data, target_idx):
        """
        保存调试数据
        :param data: 要保存的数据
        :param target_idx: 目标索引 (may not be very relevant if crawler is for one target)
        """
        try:
            # Use a generic name or include task-specific ID if available through active_api_client
            task_identifier = "unknown_task"
            if self.api_client_config_template and hasattr(self.api_client_config_template, 'task_id_for_logging'): # Hypothetical
                task_identifier = self.api_client_config_template.task_id_for_logging
            elif self.targets and target_idx < len(self.targets):
                 target = self.targets[target_idx]
                 task_identifier = f"{target.get('origin','NA')}_{target.get('destination','NA')}"


            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") # Added microseconds for uniqueness
            file_path = os.path.join(
                self.debug_dir,
                f"crawler_debug_{task_identifier}_{timestamp}.json"
            )

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            # print(f"[抓取器] 调试数据已保存: {file_path}") # Can be noisy

        except Exception as e:
            self._safe_print(f"[抓取器] 保存调试数据失败: {e}")

    def _stats_loop(self):
        """统计线程循环，定期打印统计信息"""
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis} Stats]"
        last_print_time = time.time()
        last_total_requests = 0

        try:
            while self.running: # Loop as long as the crawler is running
                current_time = time.time()
                if current_time - last_print_time >= 10: # Print stats every 10 seconds
                    with self.lock: # Ensure thread-safe access to stats
                        total_req = self.stats["total_requests"]
                        successful_req = self.stats["successful_requests"]
                        failed_req = self.stats["failed_requests"]
                        retried_req = self.stats["retried_requests"]

                    # Calculate requests per second since last print
                    elapsed_time_stats = current_time - last_print_time
                    requests_this_interval = total_req - last_total_requests
                    rps = requests_this_interval / elapsed_time_stats if elapsed_time_stats > 0 else 0

                    last_total_requests = total_req
                    last_print_time = current_time

                    self._safe_print(f"{log_prefix} 总请求: {total_req}, 成功: {successful_req}, "
                                   f"失败: {failed_req}, 重试: {retried_req}, "
                                   f"速率: {rps:.2f} 请求/秒")

                time.sleep(1) # Check self.running more frequently than printing stats
        except Exception as e:
            self._safe_print(f"{log_prefix} 统计线程出错: {e}")
        finally:
            self._safe_print(f"{log_prefix} 统计线程结束")


    def _print_final_stats(self):
        """打印最终统计信息"""
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis} FinalStats]"
        if not self.stats["start_time"] or self.stats["total_requests"] == 0 : # Avoid division by zero if no requests
            self._safe_print(f"{log_prefix} 无请求或未开始。")
            return

        end_time = self.stats["end_time"] or datetime.now()
        duration = (end_time - self.stats["start_time"]).total_seconds()
        if duration <= 0: duration = 0.01 # Avoid division by zero if duration is too short

        with self.lock: # Ensure thread-safe access
            total_requests = self.stats["total_requests"]
            successful_requests = self.stats["successful_requests"]
            failed_requests = self.stats["failed_requests"]
            retried_requests = self.stats["retried_requests"]

        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        failure_rate = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        requests_per_second = total_requests / duration

        self._safe_print("\n" + "="*50)
        self._safe_print(f"{log_prefix}")
        self._safe_print(f"运行时间: {duration:.2f} 秒")
        self._safe_print(f"总请求数: {total_requests}")
        self._safe_print(f"成功请求: {successful_requests} ({success_rate:.2f}%)")
        self._safe_print(f"失败请求: {failed_requests} ({failure_rate:.2f}%)")
        self._safe_print(f"重试请求: {retried_requests}")
        self._safe_print(f"平均速率: {requests_per_second:.2f} 请求/秒")
        self._safe_print("="*50 + "\n")

    def is_alive_internal(self):
        # Check if the main worker_thread is alive
        if not self.running and (not self.worker_thread or not self.worker_thread.is_alive()):
             return False # Explicitly stopped or never started properly
        if self.worker_thread and self.worker_thread.is_alive():
            return True
        # If worker_thread is None or not alive, but running is True, it might be in startup/error
        # Consider it not "fully" alive for monitoring purposes if worker isn't up.
        return self.running # Fallback to self.running if thread object is complex to assess

# 命令行直接运行时的入口
if __name__ == "__main__":
    # This main block is for testing the crawler independently.
    # It will need a valid token and execution_id to run.
    print("高并发抓取器测试")

    # --- 配置 ---
    # 重要: 请替换为有效的测试token和execution_id
    TEST_TOKEN = "YOUR_VALID_IBEOPENTOKEN_HERE"  # 替换!
    TEST_EXECUTION_ID = "YOUR_VALID_EXECUTION_ID_HERE"  # 替换!

    if TEST_TOKEN == "YOUR_VALID_IBEOPENTOKEN_HERE" or TEST_EXECUTION_ID == "YOUR_VALID_EXECUTION_ID_HERE":
        print("错误: 请在 high_concurrency_crawler.py 的 __main__ 部分设置有效的 TEST_TOKEN 和 TEST_EXECUTION_ID。")
        sys.exit(1)

    # 创建一个临时的API客户端实例用于测试
    test_api_client = VAApiClient()
    test_api_client.token = TEST_TOKEN
    test_api_client.execution_id = TEST_EXECUTION_ID
    # test_api_client.proxies = ["*********************:port"] # 可选: 配置代理
    # test_api_client.use_proxy = True


    crawler = HighConcurrencyCrawler(max_workers=5, max_retries=2) # 低并发测试

    # 添加测试目标 (示例: PVG-SYD)
    # crawler.add_target("PVG", "SYD", "2025-03-10", "VA5008+VA825") # 示例，可能无此航班
    crawler.add_target("SYD", "MEL", "2024-09-25", "VA811") # 替换为你想测试的实际存在的航班和日期


    # 设置回调函数
    def found_callback(match_info, task_id_cbk):
        print("\n" + "*"*20 + " 测试回调: 找到匹配航班! " + "*"*20)
        print(json.dumps(match_info, indent=2, ensure_ascii=False))
        print("*"*60 + "\n")
        # 在回调中停止爬虫，因为我们只关心第一次匹配
        # crawler.stop() # Crawler now stops itself upon first match if callback is successful

    crawler.set_callback(found_callback)

    # 启动抓取 (传入测试API客户端)
    if crawler.start(task_id_for_redis="test_hcc_task", initial_api_client_template=test_api_client):
        print("测试抓取器已启动。按 Ctrl+C 停止...")

        # 保持主线程运行，直到找到匹配或手动停止
        try:
            while crawler.running and not crawler.found_match:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n测试抓取器被用户中断...")
        finally:
            if crawler.running:
                crawler.stop()
    else:
        print("测试抓取器启动失败。")

    print("测试结束。")
