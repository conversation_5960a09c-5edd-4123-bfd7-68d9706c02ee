import tkinter as tk
from tkinter import messagebox, filedialog, ttk
from threading import Thread, current_thread, main_thread
from api_client import VAApiClient
import sys
import os
sys.path.append("..")
from datetime import datetime, timedelta
import random
import string
import time
import json
import pyperclip
from high_concurrency_crawler import HighConcurrencyCrawler

import asyncio
import websockets
import redis
import queue
import threading
import urllib.parse

# --- Reese84 Generation Configuration (as defined in high_concurrency_crawler.py) ---
# Ensure this URL is correct and consistent.
AKAMAI_CHALLENGE_URL_FOR_REESE84 = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'

# --- Redis Configuration ---
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = None # Set if your Redis has a password
REDIS_COOKIE_EXPIRY_SECONDS = 15 * 60  # 15 minutes
REDIS_REFRESH_THRESHOLD_SECONDS = 1 * 60 # Refresh if TTL is less than 1 minute

_redis_client = None

def get_redis_client():
    global _redis_client
    if _redis_client is None:
        try:
            _redis_client = redis.Redis(
                host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB,
                password=REDIS_PASSWORD, decode_responses=True
            )
            _redis_client.ping()
            print(f"[*] 成功连接到Redis: {REDIS_HOST}:{REDIS_PORT}")
            app = RedeemApp.get_instance()
            if app: app.log_message(f"成功连接到Redis: {REDIS_HOST}:{REDIS_PORT}", "green")
        except redis.exceptions.ConnectionError as e:
            print(f"[!!!] 连接Redis失败: {e}")
            app = RedeemApp.get_instance()
            if app: app.log_message(f"连接Redis失败: {e}", "red", True)
            else: messagebox.showerror("Redis连接错误", f"无法连接到Redis: {e}\nLog queue not available.")
            _redis_client = None
    return _redis_client

def get_task_cookie_redis_key(task_id):
    return f"task:{task_id}:cookies"

# Global WebSocket state
connected_clients = set()

async def ws_handler(websocket, path):
    app_instance = RedeemApp.get_instance()
    connected_clients.add(websocket)
    if app_instance: app_instance.log_message(f"WebSocket客户端连接: {websocket.remote_address}", "blue")
    else: print(f"[ws_handler] app_instance is None, cannot log connection for {websocket.remote_address}")

    try:
        async for message in websocket:
            if app_instance:
                app_instance.log_message(f"WebSocket收到: {message[:80]}...", "blue") # Log more of the message
            else:
                print(f"[ws_handler] app_instance is None, received message: {message[:80]}...")

            try:
                data = json.loads(message)
                if data.get("type") == "cookie_data":
                    if app_instance and hasattr(app_instance, 'main_loop') and app_instance.main_loop.is_running():
                        # Schedule route_extension_cookies to run in the main asyncio loop
                        app_instance.log_message("准备将 route_extension_cookies 调度到主循环...", "debug")
                        app_instance.main_loop.call_soon_threadsafe(
                            asyncio.create_task,
                            app_instance.route_extension_cookies(data)
                        )
                        await websocket.send(json.dumps({"status": "success", "message": "Cookie数据已收到并已开始调度处理"}))
                    elif app_instance: # main_loop not running or not available, try direct call (might risk UI issues if not careful)
                        app_instance.log_message("主循环未运行或不可用，尝试直接调用 route_extension_cookies (有风险)。", "yellow")
                        await app_instance.route_extension_cookies(data) # Fallback, but risky if it does UI directly
                        await websocket.send(json.dumps({"status": "success", "message": "Cookie数据已收到并直接路由(有风险)"}))
                    else:
                        print(f"[ws_handler] app_instance is None, cannot process cookie_data.")
                        await websocket.send(json.dumps({"status": "error", "message": "服务器端应用实例不可用"}))

                elif data.get("type") == "status_update":
                    log_msg = data.get('message', '未知状态')
                    details = data.get('details', None)
                    log_entry = f"扩展状态: {log_msg}"
                    if details: log_entry += f" (详情: {details})"
                    if app_instance: app_instance.log_message(log_entry, "blue")
                    else: print(f"[ws_handler] Ext Status: {log_entry}")
                else:
                    err_msg = f"WebSocket收到未知消息类型: {data.get('type')}"
                    if app_instance: app_instance.log_message(err_msg, "yellow")
                    else: print(f"[ws_handler] {err_msg}")
                    await websocket.send(json.dumps({"status": "error", "message": "未知消息类型"}))
            except json.JSONDecodeError:
                err_msg = f"WebSocket收到无效的JSON消息: {message[:80]}..."
                if app_instance: app_instance.log_message(err_msg, "red")
                else: print(f"[ws_handler] {err_msg}")
                await websocket.send(json.dumps({"status": "error", "message": "无效的JSON格式"}))
            except Exception as e_inner:
                err_msg = f"处理WebSocket消息时出错: {e_inner}"
                if app_instance: app_instance.log_message(err_msg, "red", True)
                try:
                    await websocket.send(json.dumps({"status": "error", "message": f"处理消息时出错: {str(e_inner)}"}))
                except Exception as e_send_err: print(f"[ws_handler] 发送错误回执失败: {e_send_err}")
    except websockets.exceptions.ConnectionClosedError:
        # Normal closure or error, already logged by app_instance if it exists
        if app_instance: app_instance.log_message(f"WebSocket客户端断开 (ClosedError): {websocket.remote_address}", "yellow")
        else: print(f"[ws_handler] Client disconnected (ClosedError): {websocket.remote_address}")
    except websockets.exceptions.ConnectionClosedOK:
        if app_instance: app_instance.log_message(f"WebSocket客户端正常断开 (ClosedOK): {websocket.remote_address}", "blue")
        else: print(f"[ws_handler] Client disconnected (ClosedOK): {websocket.remote_address}")
    except Exception as e_outer:
        # Catchall for other errors in the handler loop
        err_msg_outer = f"WebSocket连接出现外部异常: {e_outer}"
        if app_instance: app_instance.log_message(err_msg_outer, "red", True)
        else: print(f"[ws_handler] {err_msg_outer}"); import traceback; traceback.print_exc()
    finally:
        if websocket in connected_clients:
            connected_clients.remove(websocket)
        if app_instance: app_instance.log_message(f"WebSocket客户端已移除: {websocket.remote_address}", "yellow")
        else: print(f"[ws_handler] Client removed: {websocket.remote_address}")

async def start_websocket_server(host='127.0.0.1', port=8765):
    app_instance = RedeemApp.get_instance()
    try:
        async with websockets.serve(ws_handler, host, port) as server:
            if app_instance: app_instance.log_message(f"WebSocket服务器运行于 ws://{host}:{port}", "green")
            await asyncio.Future()
    except OSError as e:
        log_msg = f"启动WS服务器失败 (OSError): {e}"
        if hasattr(e, 'winerror') and e.winerror == 10048 or "Address already in use" in str(e):
            log_msg = f"启动WS服务器失败: 端口 {port} 已被占用。"
        if app_instance: app_instance.log_message(log_msg, "red", True)
        raise

def run_server_in_thread():
    app_instance = RedeemApp.get_instance()
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        if app_instance: app_instance.log_message("WS服务器线程尝试启动...", "blue")
        loop.run_until_complete(start_websocket_server())
    except OSError as e:
        if app_instance: app_instance.log_message(f"WS服务器线程启动失败 (OSError): {e}", "red", True)
    except Exception as e:
        if app_instance: app_instance.log_message(f"WS服务器线程意外终止: {e}", "red", True)

async def send_ws_command_to_clients_async(command_data):
    app_instance = RedeemApp.get_instance()
    message_to_send = json.dumps(command_data) # Define message_to_send here
    if not connected_clients:
        if app_instance: app_instance.log_message("WS: 无连接客户端发送指令。", "yellow")
        return False
    client_to_send_to = next(iter(connected_clients), None)
    if not client_to_send_to:
        if app_instance: app_instance.log_message("WS: 无有效客户端发送指令 (iter failed)。", "yellow")
        return False
    try:
        await client_to_send_to.send(message_to_send)
        if app_instance: app_instance.log_message(f"指令已发送至客户端 {client_to_send_to.remote_address}. ({message_to_send[:60]}...)", "green")
        return True
    except Exception as e:
        if app_instance: app_instance.log_message(f"向WS客户端发送指令失败: {e}", "yellow", True)
        return False

class Task:
    def __init__(self, task_id, params_dict):
        self.id = task_id
        self.params = params_dict
        self.status = "pending_setup"
        self.last_cookie_refresh_time = None
        self.cookie_expiry_duration = timedelta(minutes=14)
        self.high_concurrency_crawler = None
        self.crawler_running = False
        self.last_log_time = datetime.now()
        self.log_messages = []
        self.cookie_processed_event = asyncio.Event()
        # Ensure params has default for auto_start_crawler and auto_book_on_find if not provided by parser
        if 'auto_start_crawler' not in self.params: self.params['auto_start_crawler'] = False
        if 'auto_book_on_find' not in self.params: self.params['auto_book_on_find'] = True # Default to True based on UI logic
        if 'use_points_for_tax' not in self.params: self.params['use_points_for_tax'] = False

    def get_redis_key(self):
        return get_task_cookie_redis_key(self.id)

    def log_task_message(self, message, color="black", app_instance_override=None, include_traceback=False):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry_console = f"[{timestamp}] [任务 {self.id}] {message}"
        full_log_message_for_window = f"[{timestamp}] {message}"
        if include_traceback:
            tb = traceback.format_exc()
            full_log_message_for_window += f"\n{tb}"
        self.log_messages.append(full_log_message_for_window)
        if len(self.log_messages) > 100: self.log_messages.pop(0)
        app = app_instance_override or RedeemApp.get_instance()
        if app:
            app.log_message(f"[任务 {self.id}] {message}", color, include_traceback=include_traceback)
        else:
            print(log_entry_console)
            if include_traceback and not app : import traceback; print(traceback.format_exc()) # Print TB if no app to handle it
        self.last_log_time = datetime.now()

    def needs_cookie_refresh(self, r_client=None):
        local_r_client = r_client or get_redis_client()
        if not local_r_client:
            self.log_task_message("Redis不可用，依赖时间戳刷新Cookie。", "yellow", app_instance_override=RedeemApp.get_instance())
            if not self.last_cookie_refresh_time: return True
            return datetime.now() > (self.last_cookie_refresh_time + self.cookie_expiry_duration)
        cookie_key = self.get_redis_key()
        try:
            if not local_r_client.exists(cookie_key):
                self.log_task_message("Redis中未找到Cookie，需获取。", "yellow", app_instance_override=RedeemApp.get_instance())
                return True
            ttl = local_r_client.ttl(cookie_key)
            if ttl is None or ttl < REDIS_REFRESH_THRESHOLD_SECONDS:
                self.log_task_message(f"Redis Cookie TTL ({ttl}s) 过低，需刷新。", "yellow", app_instance_override=RedeemApp.get_instance())
                return True
        except redis.exceptions.RedisError as e:
            self.log_task_message(f"检查Redis TTL出错: {e}。依赖时间戳。", "red", include_traceback=True, app_instance_override=RedeemApp.get_instance())
            if not self.last_cookie_refresh_time: return True
            return datetime.now() > (self.last_cookie_refresh_time + self.cookie_expiry_duration)
        return False

    def store_cookies_in_redis(self, extension_data, r_client=None):
        local_r_client = r_client or get_redis_client()
        app_ref = RedeemApp.get_instance() # For logging
        if not local_r_client:
            self.log_task_message("Redis不可用，无法存储Cookie。", "red", app_instance_override=app_ref); self.status = "error_redis_unavailable"; return False
        self.log_task_message(f"准备将扩展Cookie存入Redis...", "blue", app_instance_override=app_ref)
        if not (extension_data and extension_data.get("type") == "cookie_data"):
            self.log_task_message("无效扩展数据，无法存入Redis。", "yellow", app_instance_override=app_ref); self.status = "error_cookie_missing_data"; return False

        cookies_list = extension_data.get("payload")
        execution_id_ext = extension_data.get("execution_id")
        user_agent_ext = extension_data.get("user_agent")
        found_ibeopentoken = next((c.get("value") for c in cookies_list if isinstance(c, dict) and c.get("name","").lower()=="ibeopentoken"), None) if cookies_list else None

        if not execution_id_ext or not found_ibeopentoken:
            self.log_task_message("关键Cookie信息(ExecID/Token)缺失，无法存入Redis。", "red", app_instance_override=app_ref); self.status = "error_cookie_incomplete"; return False

        redis_data = {"execution_id": str(execution_id_ext), "token": found_ibeopentoken,
                      "user_agent": str(user_agent_ext) if user_agent_ext else None,
                      "cookies_list_json": json.dumps(cookies_list or [])}
        try:
            local_r_client.set(self.get_redis_key(), json.dumps(redis_data), ex=REDIS_COOKIE_EXPIRY_SECONDS)
            self.log_task_message(f"Cookie数据已存入Redis (Expiry: {REDIS_COOKIE_EXPIRY_SECONDS}s).", "green", app_instance_override=app_ref)
            self.last_cookie_refresh_time = datetime.now()
            self.status = "cookie_valid_redis"
            return True
        except redis.exceptions.RedisError as e:
            self.log_task_message(f"存入Cookie到Redis出错: {e}", "red", True, app_instance_override=app_ref); self.status = "error_redis_store_failed"; return False

    def load_auth_data_from_redis(self, r_client=None):
        local_r_client = r_client or get_redis_client()
        app_ref = RedeemApp.get_instance() # For logging
        if not local_r_client:
            self.log_task_message("Redis不可用，无法加载认证数据。", "yellow", app_instance_override=app_ref); return None
        cookie_key = self.get_redis_key()
        try:
            stored_json = local_r_client.get(cookie_key)
            if not stored_json: self.log_task_message(f"Redis未找到认证数据 (Key: {cookie_key})。", "yellow", app_instance_override=app_ref); return None
            stored_data = json.loads(stored_json)
            cookies_list_json = stored_data.get("cookies_list_json")
            auth_package = {
                "token": stored_data.get("token"),
                "execution_id": stored_data.get("execution_id"),
                "user_agent": stored_data.get("user_agent"),
                "cookies_list": json.loads(cookies_list_json) if cookies_list_json else []
            }
            return auth_package
        except redis.exceptions.RedisError as e:
            self.log_task_message(f"从Redis加载认证数据出错: {e}", "red", True, app_instance_override=app_ref); return None
        except json.JSONDecodeError as e:
            self.log_task_message(f"解析Redis中认证数据JSON失败: {e}", "red", True, app_instance_override=app_ref)
            try: local_r_client.delete(cookie_key)
            except: pass
            return None

class RedeemApp:
    _instance = None
    def __init__(self, root_window):
        self.root = root_window; RedeemApp._instance = self
        self.root.title("维珍ANA兑换助手 (多任务Redis版)")
        self.log_queue = queue.Queue()
        self.debug_mode = True
        self.tasks = []; self.next_task_id = 1
        self.cookie_request_queue = asyncio.Queue()
        self.active_cookie_request_task_id = None
        self.cookie_processing_lock = asyncio.Lock()
        self.global_va_client_config = VAApiClient()
        self.concurrency_var = tk.StringVar(value="3")
        self.use_points_for_tax_var = tk.BooleanVar(value=False)
        self.ws_server_thread = None
        try: self.main_loop = asyncio.get_event_loop()
        except RuntimeError: self.main_loop = asyncio.new_event_loop(); asyncio.set_event_loop(self.main_loop)
        self.start_internal_websocket_server()
        self.main_loop.create_task(self.cookie_acquisition_worker())
        self.main_loop.create_task(self.task_monitoring_worker())
        self.build_ui()
        get_redis_client()
        self.log_message("应用启动 (DI + Redis + LogQueue)。")

    @classmethod
    def get_instance(cls): return cls._instance

    def start_internal_websocket_server(self):
        if self.ws_server_thread is None or not self.ws_server_thread.is_alive():
            self.ws_server_thread = Thread(target=run_server_in_thread, daemon=True)
            self.ws_server_thread.start()
            self.log_message("[*] WebSocket服务器线程已启动。", "blue")

    async def cookie_acquisition_worker(self):
        self.log_message("Cookie获取工作者启动。", "blue")
        while True:
            task_id_to_process = None; task_obj_worker = None
            try:
                task_id_to_process = await self.cookie_request_queue.get()
                task_obj_worker = self.get_task_by_id(task_id_to_process)
                if not task_obj_worker: self.log_message(f"CW: 任务 {task_id_to_process} 未找到。", "yellow")
                else:
                    task_obj_worker.cookie_processed_event.clear()
                    async with self.cookie_processing_lock:
                        self.active_cookie_request_task_id = task_obj_worker.id
                        task_obj_worker.status = "pending_browser_cookie"; task_obj_worker.log_task_message("CW: 开始获取Cookie...")
                    target_url = self._construct_target_url_for_task(task_obj_worker)
                    if not target_url:
                        task_obj_worker.log_task_message("CW: 构建URL失败"); task_obj_worker.status = "error_url_build_failed"
                        task_obj_worker.cookie_processed_event.set()
                        async with self.cookie_processing_lock:
                            if self.active_cookie_request_task_id == task_obj_worker.id: self.active_cookie_request_task_id = None
                    else:
                        command = {"action": "navigate_and_get_cookies", "url": target_url}
                        if task_obj_worker.params.get("credentials"): command["credentials"] = task_obj_worker.params["credentials"]
                        sent_ok = await send_ws_command_to_clients_async(command)
                        if sent_ok:
                            task_obj_worker.log_task_message("CW: 获取Cookie指令已发送，等待响应...")
                            try: await asyncio.wait_for(task_obj_worker.cookie_processed_event.wait(), timeout=180.0)
                            except asyncio.TimeoutError:
                                task_obj_worker.log_task_message("CW: 等待Cookie处理事件超时(180s)。", "red"); task_obj_worker.status = "error_cookie_timeout"
                                async with self.cookie_processing_lock:
                                    if self.active_cookie_request_task_id == task_obj_worker.id: self.active_cookie_request_task_id = None
                        else:
                            task_obj_worker.log_task_message("CW: 发送指令失败。", "yellow"); task_obj_worker.status = "error_ws_send_failed"
                            task_obj_worker.cookie_processed_event.set()
                            async with self.cookie_processing_lock:
                                if self.active_cookie_request_task_id == task_obj_worker.id: self.active_cookie_request_task_id = None
            except Exception as e:
                self.log_message(f"CW错误 (处理任务 {task_id_to_process}): {e}", "red", True)
                if task_obj_worker:
                    task_obj_worker.status = "error_cookie_worker_exc"
                    if hasattr(task_obj_worker, 'cookie_processed_event'): task_obj_worker.cookie_processed_event.set()
                async with self.cookie_processing_lock:
                    if self.active_cookie_request_task_id == task_id_to_process: self.active_cookie_request_task_id = None
            finally:
                if task_obj_worker: self.update_task_list_ui()
                if task_id_to_process is not None: self.cookie_request_queue.task_done()

    async def task_monitoring_worker(self):
        self.log_message("任务监控工作者启动。", "blue")
        r_client_monitor = get_redis_client()
        while True:
            try:
                for task_obj_monitor in list(self.tasks): # Iterate over a copy
                    if task_obj_monitor.status not in ["pending_browser_cookie", "pending_cookie_refresh"] and \
                       task_obj_monitor.needs_cookie_refresh(r_client_monitor):

                        task_obj_monitor.log_task_message("TMW: Cookie需刷新，加入队列。")
                        task_obj_monitor.status = "pending_cookie_refresh"
                        await self.cookie_request_queue.put(task_obj_monitor.id)
                        self.update_task_list_ui() # Update UI after queueing

                    # Check if crawler for this task died unexpectedly
                    if task_obj_monitor.crawler_running and \
                       task_obj_monitor.high_concurrency_crawler and \
                       hasattr(task_obj_monitor.high_concurrency_crawler, 'is_alive_internal') and \
                       not task_obj_monitor.high_concurrency_crawler.is_alive_internal():

                        task_obj_monitor.log_task_message("TMW: 检测到挂单爬虫意外停止。", "red")
                        task_obj_monitor.crawler_running = False
                        task_obj_monitor.status = "error_crawler_died"
                        self.update_task_list_ui()

                await asyncio.sleep(20)  # Check every 20 seconds
            except Exception as e:
                self.log_message(f"TMW错误: {e}", "red", True)
                await asyncio.sleep(60) # Wait longer after an error

    def build_ui(self):
        control_frame = ttk.Frame(self.root, padding="10"); control_frame.grid(row=0, column=0, sticky="nsew")
        self.tasks_frame = ttk.LabelFrame(self.root, text="任务列表", padding="10"); self.tasks_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        log_frame_main = ttk.LabelFrame(self.root, text="全局日志", padding="10"); log_frame_main.grid(row=2, column=0, sticky="nsew", padx=10, pady=5)
        self.root.grid_rowconfigure(1, weight=3); self.root.grid_rowconfigure(2, weight=1); self.root.grid_columnconfigure(0, weight=1)
        input_area_frame = ttk.LabelFrame(control_frame, text="格式化输入 (新任务)"); input_area_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        self.formatted_input_text = tk.Text(input_area_frame, height=10, width=70); self.formatted_input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        input_scrollbar = ttk.Scrollbar(input_area_frame, orient=tk.VERTICAL, command=self.formatted_input_text.yview); input_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.formatted_input_text['yscrollcommand'] = input_scrollbar.set
        input_button_frame = ttk.Frame(control_frame); input_button_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        ttk.Button(input_button_frame, text="添加为新任务", command=self.add_new_task_from_input, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(input_button_frame, text="清空输入", command=self.clear_formatted_input, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(input_button_frame, text="填充示例", command=self.fill_example_data, width=15).pack(side=tk.LEFT, padx=5)
        concurrency_frame = ttk.Frame(control_frame); concurrency_frame.grid(row=2, column=0, sticky="w", padx=5, pady=5)
        ttk.Label(concurrency_frame, text="新任务默认并发数:").pack(side=tk.LEFT, padx=(0,2))
        concurrency_entry = ttk.Entry(concurrency_frame, textvariable=self.concurrency_var, width=5); concurrency_entry.pack(side=tk.LEFT)
        ttk.Checkbutton(concurrency_frame, text="自动抵扣税金", variable=self.use_points_for_tax_var).pack(side=tk.LEFT, padx=10)
        self.task_list_canvas = tk.Canvas(self.tasks_frame); self.task_list_scrollbar = ttk.Scrollbar(self.tasks_frame, orient="vertical", command=self.task_list_canvas.yview)
        self.scrollable_frame_tasks = ttk.Frame(self.task_list_canvas)
        self.scrollable_frame_tasks.bind("<Configure>", lambda e: self.task_list_canvas.configure(scrollregion=self.task_list_canvas.bbox("all")))
        self.task_list_canvas.create_window((0, 0), window=self.scrollable_frame_tasks, anchor="nw"); self.task_list_canvas.configure(yscrollcommand=self.task_list_scrollbar.set)
        self.task_list_canvas.pack(side="left", fill="both", expand=True); self.task_list_scrollbar.pack(side="right", fill="y")
        self.log_text = tk.Text(log_frame_main, height=10, width=80, wrap=tk.WORD, state=tk.DISABLED); self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        log_scrollbar_main = ttk.Scrollbar(log_frame_main, command=self.log_text.yview); log_scrollbar_main.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text['yscrollcommand'] = log_scrollbar_main.set
        self.log_text.tag_configure("error", foreground="red"); self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("warning", foreground="#B8860B"); self.log_text.tag_configure("info", foreground="blue")
        self.update_task_list_ui()
        self.log_message("UI构建完成。", "debug")

    def add_new_task_from_input(self):
        parsed_data = self._parse_formatted_input_for_task()
        if not parsed_data: return
        task_id = self.next_task_id; self.next_task_id += 1
        new_task = Task(task_id, parsed_data)
        self.tasks.append(new_task)
        self.log_message(f"已添加新任务 {task_id}: {new_task.params.get('origin')}-{new_task.params.get('destination')}", "green")
        self.update_task_list_ui()
        if new_task.params.get("credentials"):
            self.log_message(f"任务 {task_id} 含凭据，自动请求Cookie。", "blue")
            self.request_cookie_for_task(task_id)

    def get_task_by_id(self, task_id):
        for task_obj in self.tasks:
            if task_obj.id == task_id:
                return task_obj
        return None

    def get_auth_data_for_task(self, task_id):
        task_obj = self.get_task_by_id(task_id)
        if not task_obj:
            self.log_message(f"get_auth_data: 任务 {task_id} 未找到 (爬虫请求)。", "yellow")
            return None
        r_client = get_redis_client()
        auth_data = task_obj.load_auth_data_from_redis(r_client)
        return auth_data

    async def route_extension_cookies(self, extension_data):
        r_client_route = get_redis_client()
        task_being_processed_id = None
        task_obj_route = None
        try:
            async with self.cookie_processing_lock:
                if self.active_cookie_request_task_id is None:
                    self.log_message("WS->R: 无活动Cookie请求ID，忽略。", "yellow"); return
                task_being_processed_id = self.active_cookie_request_task_id
                task_obj_route = self.get_task_by_id(task_being_processed_id)
                if not task_obj_route:
                    self.log_message(f"WS->R: 未找到任务 {task_being_processed_id}，清除活动ID。", "yellow")
                    self.active_cookie_request_task_id = None
                    return

                self.log_message(f"WS->R: 路由扩展Cookie到任务 {task_obj_route.id} (存入Redis)...", "blue")
                success = task_obj_route.store_cookies_in_redis(extension_data, r_client_route)
                if success:
                    self.log_message(f"WS->R: 任务 {task_obj_route.id} Cookie已存入Redis。", "green")
                    if task_obj_route.status == "cookie_valid_redis" and task_obj_route.params.get("auto_start_crawler", False):
                        self.log_message(f"任务 {task_obj_route.id}: Cookie有效且配置自动启动，开始挂单...", "blue")
                        self.main_loop.call_soon_threadsafe(self.start_task_crawler, task_obj_route.id)
                else:
                    self.log_message(f"WS->R: 任务 {task_obj_route.id} Cookie存入Redis失败。状态: {task_obj_route.status}", "red")
                self.active_cookie_request_task_id = None

            if task_obj_route:
                task_obj_route.cookie_processed_event.set()

        except Exception as e:
            self.log_message(f"route_extension_cookies 错误 (任务 {task_being_processed_id}): {e}", "red", True)
            if task_obj_route and hasattr(task_obj_route, 'cookie_processed_event'):
                task_obj_route.cookie_processed_event.set()
            if self.active_cookie_request_task_id == task_being_processed_id:
                 async with self.cookie_processing_lock:
                    if self.active_cookie_request_task_id == task_being_processed_id:
                         self.active_cookie_request_task_id = None
        finally:
            self.update_task_list_ui()

    def start_task_crawler(self, task_id):
        task_obj = self.get_task_by_id(task_id)
        if not task_obj:
            self.log_message(f"启动挂单失败: 任务 {task_id} 未找到。", "red"); return
        if task_obj.crawler_running:
            task_obj.log_task_message("挂单已在运行。"); return

        auth_data_check = self.get_auth_data_for_task(task_id)
        if not auth_data_check or not auth_data_check.get("token") or not auth_data_check.get("execution_id"):
            task_obj.log_task_message("无法启动挂单: Redis中Cookie无效或不完整。请先获取有效Cookie。")
            task_obj.status="error_redis_cookie_invalid"; self.update_task_list_ui(); return

        if not (task_obj.params.get("origin") and task_obj.params.get("destination") and task_obj.params.get("date_str")):
            task_obj.log_task_message("无法启动挂单: 缺少O/D/Date。"); return
        target_flight_nos_str = task_obj.params.get("flight_no_str", "").strip()
        if not target_flight_nos_str:
            task_obj.log_task_message("无法启动挂单: 目标航班号为空。"); return

        try:
            conc = int(self.concurrency_var.get()); conc = max(1, conc)
        except ValueError:
            conc = 3; self.concurrency_var.set("3")

        # 转换日期格式：从YYYYMMDD转换为YYYY-MM-DD
        date_str_raw = task_obj.params["date_str"]
        try:
            dt_obj = datetime.strptime(date_str_raw, "%Y%m%d")
            api_date_format = dt_obj.strftime("%Y-%m-%d")
            task_obj.log_task_message(f"日期格式转换: {date_str_raw} → {api_date_format}")
        except ValueError:
            task_obj.log_task_message(f"日期格式错误: {date_str_raw}，应为YYYYMMDD格式", "red")
            task_obj.status = "error_date_format"
            self.update_task_list_ui()
            return

        task_obj.high_concurrency_crawler = HighConcurrencyCrawler(max_workers=conc, app_instance_ref=self)
        task_obj.high_concurrency_crawler.add_target(task_obj.params["origin"], task_obj.params["destination"], api_date_format, target_flight_nos_str)
        task_obj.high_concurrency_crawler.set_callback(self.on_flight_found_for_task)

        if task_obj.high_concurrency_crawler.start(task_id_for_redis=task_obj.id, initial_api_client_template=self.global_va_client_config):
            task_obj.crawler_running = True; task_obj.status = "running_crawler"
            task_obj.log_task_message(f"开始高并发挂单 (并发: {conc})")
        else:
            task_obj.status = "error_crawler_start_failed"; task_obj.log_task_message("启动挂单失败。")
        self.update_task_list_ui()

    def on_flight_found_for_task(self, match_info, task_id_from_crawler):
        print(f"[THREADING DEBUG] RedeemApp.on_flight_found_for_task called for task {task_id_from_crawler} from thread: {threading.current_thread().name}")
        self.log_message(f"任务 {task_id_from_crawler}: 收到爬虫找到航班的回调，准备调度到主线程处理。", "debug")
        if hasattr(self, 'main_loop') and self.main_loop.is_running():
            self.main_loop.call_soon_threadsafe(
                self._handle_flight_found_on_main_thread,
                match_info,
                task_id_from_crawler
            )
        else:
            self.log_message(f"错误: 任务 {task_id_from_crawler} 找到航班，但无法调度到主线程处理 (main_loop不可用)。", "red", True)

    def _handle_flight_found_on_main_thread(self, match_info, task_id_from_crawler):
        # This method will now be executed in the main asyncio/Tkinter thread context
        self.log_message(f"任务 {task_id_from_crawler}: _handle_flight_found_on_main_thread 开始执行。", "debug")
        task_obj = self.get_task_by_id(task_id_from_crawler)
        if not task_obj:
            self.log_message(f"主线程处理找到航班: 任务 {task_id_from_crawler} 未找到！", "red", True)
            return

        task_obj.log_task_message("匹配成功! 主线程处理回调。")
        flight_sig = match_info.get("flight_signature", {})
        task_obj.log_task_message(f"航班: {flight_sig.get('offer_flight_numbers_found', [])}, 舱位: {flight_sig.get('offer_cabin_classes_found', [])}")
        proxy_that_worked = match_info.get('proxy_used_for_success')
        task_obj.log_task_message(f"成功搜索使用的代理: {proxy_that_worked.get('http') if proxy_that_worked else '未记录'}", "info")

        task_obj.crawler_running = False
        task_obj.status = "found_match"
        # Crawler should have stopped itself, but ensure it here too if reference exists.
        if task_obj.high_concurrency_crawler and hasattr(task_obj.high_concurrency_crawler, 'stop'):
            # No need to call stop() again if crawler already sets self.running = False and exits its loops.
            # task_obj.high_concurrency_crawler.stop() # This might try to join threads which could be problematic if called from event loop directly.
            pass # Assuming crawler stops itself based on self.running flag.

        self.log_message(f"任务 {task_id_from_crawler}: 状态更新为 found_match，准备更新UI。", "debug")
        self.update_task_list_ui() # This is now safe

        auto_book_flag = task_obj.params.get("auto_book_on_find", True)
        self.log_message(f"任务 {task_id_from_crawler}: auto_book_on_find = {auto_book_flag}", "debug")

        if auto_book_flag:
            self.log_message(f"任务 {task_id_from_crawler}: 找到航班，准备调度自动出票 (代理: {proxy_that_worked.get('http') if proxy_that_worked else '将使用默认/无'})...", "blue")
            # self.root.after is safe because we are in the main thread now
            self.root.after(100, lambda mi=match_info, tid=task_id_from_crawler, proxy=proxy_that_worked:
                            self.auto_book_found_flight_for_task(mi, tid, proxy_for_booking=proxy))
        else:
            task_obj.log_task_message("找到航班，但未配置自动出票。")
            self.root.after(0, lambda: messagebox.showinfo("成功找到!", f"任务 {task_id_from_crawler} 找到匹配航班 (自动出票已禁用)!"))

    def auto_book_found_flight_for_task(self, match_info, task_id, proxy_for_booking=None):
        task_obj = self.get_task_by_id(task_id)
        if not task_obj: self.log_message(f"自动出票错误: 任务 {task_id} 未找到。", "red", True); return

        self.log_message(f"任务 {task_id}: 开始自动出票流程。 使用代理: {proxy_for_booking.get('http') if proxy_for_booking else '默认/轮换'}", "info")

        base_auth_data = self.get_auth_data_for_task(task_obj.id)
        if not base_auth_data or not base_auth_data.get("token") or not base_auth_data.get("execution_id"):
            task_obj.log_task_message("自动出票失败: Redis中基础Cookie数据无效或不完整。")
            task_obj.status="error_redis_cookie_invalid"; self.update_task_list_ui(); return

        reese84_value, ua_from_reese, reese_error_detail = self._generate_reese84_for_task(
            task_obj.id,
            AKAMAI_CHALLENGE_URL_FOR_REESE84, # Use the module-level constant
            proxy_to_use_for_reese=proxy_for_booking
        )
        if not reese84_value:
            error_msg = f"自动出票失败: 未能生成reese84令牌。{reese_error_detail if reese_error_detail else ''}"
            task_obj.log_task_message(error_msg)
            task_obj.status="error_reese84_failed"; self.update_task_list_ui(); return

        explicit_cookies_for_req = {'reese84': reese84_value}
        if base_auth_data.get('cookies_list'):
            dcsessionid_val = next((c.get('value') for c in base_auth_data['cookies_list'] if isinstance(c,dict) and c.get('name') == 'DCSESSIONID'), None)
            if dcsessionid_val: explicit_cookies_for_req['DCSESSIONID'] = dcsessionid_val

        effective_auth_data_for_api = base_auth_data.copy()
        if ua_from_reese: effective_auth_data_for_api['user_agent'] = ua_from_reese

        task_obj.status = "booking_in_progress"; task_obj.log_task_message("开始自动出票流程..."); self.update_task_list_ui()

        booking_api_client = VAApiClient()
        booking_api_client.use_proxy = self.global_va_client_config.use_proxy
        booking_api_client.proxies = self.global_va_client_config.proxies
        if booking_api_client.proxies: booking_api_client.set_proxy()

        try:
            original_offer_data = match_info.get("original_offer_data"); flight_signature = match_info.get("flight_signature")
            if not (original_offer_data and flight_signature and match_info.get("original_shoppingBasketHashCode") is not None):
                task_obj.log_task_message("出票数据结构不完整"); task_obj.status="error_booking_data"; self.update_task_list_ui(); return
            cabin_to_search = flight_signature.get("offer_cabin_classes_found", [task_obj.params.get("cabin", "Business")])[0]

            task_obj.log_task_message(f"二次搜索 (proxy: {proxy_for_booking.get('http') if proxy_for_booking else 'Default'}...)...")
            secondary_search_results = booking_api_client.search_flights(
                flight_signature.get('origin'), flight_signature.get('destination'), flight_signature.get('date'),
                cabin=cabin_to_search, award_booking=True,
                auth_data=effective_auth_data_for_api,
                explicit_cookies=explicit_cookies_for_req,
                use_specific_proxy=proxy_for_booking
            )
            if not (secondary_search_results and secondary_search_results.get("data", {}).get("bookingAirSearch", {}).get("originalResponse")):
                task_obj.log_task_message("二次搜索失败"); task_obj.status="error_booking_search"; self.update_task_list_ui(); return
            secondary_offers = secondary_search_results.get("data",{}).get("bookingAirSearch",{}).get("originalResponse",{}).get("unbundledOffers",[[]])[0]
            current_session_offer_data = None
            if secondary_offers:
                sig_flts = flight_signature.get("offer_flight_numbers_found", [])
                sig_cabs = flight_signature.get("offer_cabin_classes_found", [])
                for offer in secondary_offers:
                    segs = offer.get("itineraryPart",[{}])[0].get("segments",[])
                    new_flts = [f"{s.get('flight',{}).get('airlineCode')}{s.get('flight',{}).get('flightNumber')}" for s in segs]
                    new_cabs = [s.get("cabinClass","") for s in segs]
                    if sorted(new_flts) == sorted(sig_flts) and new_cabs == sig_cabs:
                        current_session_offer_data = offer; task_obj.log_task_message(f"二次搜索匹配成功: {new_flts}"); break
            if not current_session_offer_data or current_session_offer_data.get("shoppingBasketHashCode") is None:
                task_obj.log_task_message("二次搜索未能匹配或Offer无效"); task_obj.status="error_booking_match"; self.update_task_list_ui(); return
            task_obj.log_task_message(f"二次搜索确认成功. New Hash: {current_session_offer_data.get('shoppingBasketHashCode')}")

            passengers_for_api = []
            shared_email = f"{''.join(random.choices(string.ascii_lowercase + string.digits, k=10))}@example.com"
            shared_phone = f"04{''.join(random.choices(string.digits, k=8))}"
            for i, p_task_data in enumerate(task_obj.params.get("passengers_info",[])):
                p_api = {"last_name": p_task_data.get("last_name"), "first_name": p_task_data.get("first_name"), "dob": p_task_data.get("dob"), "gender": p_task_data.get("gender","MALE").upper(), "title": "MR" if p_task_data.get("gender","MALE").upper() == "MALE" else "MS", "email": shared_email, "phone": shared_phone}
                if not all(p_api.get(k) for k in ["last_name", "first_name", "dob"]): task_obj.log_task_message(f"乘客 {i+1} 信息不全"); task_obj.status="error_pax_data"; self.update_task_list_ui(); return
                passengers_for_api.append(p_api)
            if not passengers_for_api: task_obj.log_task_message("乘客信息为空"); task_obj.status="error_pax_prep"; self.update_task_list_ui(); return

            selected_award, selected_cash, selected_curr = None, None, "AUD"
            total_alts = current_session_offer_data.get("total", {}).get("alternatives", [])
            if total_alts and isinstance(total_alts[0], list) and total_alts[0]:
                for item_price in total_alts[0]:
                    if item_price.get("currency") == "FFCURRENCY": selected_award = item_price.get("amount")
                    else: selected_cash = item_price.get("amount"); selected_curr = item_price.get("currency")
                    if selected_award is not None and selected_cash is not None: break
            if selected_award is None: task_obj.log_task_message("无法解析价格"); task_obj.status="error_price_parse"; self.update_task_list_ui(); return
            award_amount, cash_amount, cash_currency = int(selected_award), float(selected_cash or 0.0), selected_curr
            task_obj.log_task_message(f"选定价格: {award_amount}积分 + {cash_amount} {cash_currency}")

            task_obj.log_task_message(f"调用 book_flight API (proxy: {proxy_for_booking.get('http') if proxy_for_booking else 'Default'}...)...")
            book_ok, book_data_msg, book_err = booking_api_client.book_flight(
                current_session_offer_data, passengers_for_api,
                award_amount, cash_amount, cash_currency,
                auth_data=effective_auth_data_for_api,
                explicit_cookies=explicit_cookies_for_req,
                use_specific_proxy=proxy_for_booking
            )
            if not book_ok: task_obj.log_task_message(f"预订失败({book_err}): {book_data_msg}"); task_obj.status=f"error_book_{book_err}"; self.update_task_list_ui(); return
            task_obj.log_task_message(f"预订步骤成功: {book_data_msg.get('message') if isinstance(book_data_msg, dict) else book_data_msg}")

            payment_cfg_api = {key: task_obj.params.get("payment_info",{})[key] for key in ["card_number", "expiry_month", "expiry_year", "cvv"]}
            payment_cfg_api["billing_address"] = task_obj.params.get("billing_address", {})
            use_pts_tax = task_obj.params.get("use_points_for_tax", self.use_points_for_tax_var.get())

            task_obj.log_task_message(f"调用 make_payment API (proxy: {proxy_for_booking.get('http') if proxy_for_booking else 'Default'}...)...")
            pay_ok, pay_data_msg, pay_err = booking_api_client.make_payment(
                payment_cfg_api, passengers_for_api,
                award_amount, cash_amount, cash_currency, use_pts_tax,
                auth_data=effective_auth_data_for_api,
                explicit_cookies=explicit_cookies_for_req,
                use_specific_proxy=proxy_for_booking
            )
            if not pay_ok: task_obj.log_task_message(f"支付失败({pay_err}): {pay_data_msg}"); task_obj.status=f"error_pay_{pay_err}"; self.update_task_list_ui(); return
            task_obj.log_task_message("支付成功!"); task_obj.status = "booked_pending_pnr"

            if isinstance(pay_data_msg,dict) and pay_data_msg.get("purchase_response"):
                self._extract_and_display_pnr_for_task(task_obj, pay_data_msg.get("purchase_response"), \
                    booking_api_client.confirmed_award_amount or award_amount, booking_api_client.confirmed_cash_amount or cash_amount, \
                    booking_api_client.confirmed_cash_currency or cash_currency, passengers_for_api)
            else: task_obj.log_task_message("支付成功但PNR响应结构错误"); task_obj.status="booked_pnr_error"
        except Exception as e:
            task_obj.log_task_message(f"自动出票过程出错: {e}",True); task_obj.status="error_booking_exception"
        finally: self.update_task_list_ui()

    def _generate_reese84_for_task(self, task_id, target_url_for_akamai=None, proxy_to_use_for_reese=None):
        self.log_message(f"任务 {task_id}: _generate_reese84_for_task 调用 (代理传入: {proxy_to_use_for_reese.get('http') if proxy_to_use_for_reese else '无'})...", "debug")

        # 检查任务是否应该停止
        task_obj = self.get_task_by_id(task_id)
        if task_obj and hasattr(task_obj, 'high_concurrency_crawler') and task_obj.high_concurrency_crawler:
            if not task_obj.high_concurrency_crawler.running:
                error_detail = "任务已停止，取消reese84生成。"
                self.log_message(f"任务 {task_id}: {error_detail}", "yellow")
                return None, None, error_detail

        reese84_token = None
        ua_from_reese_resolver = None
        error_detail = "Reese84 generation: Initial state." # Default error message

        challenge_url = target_url_for_akamai or AKAMAI_CHALLENGE_URL_FOR_REESE84
        if not challenge_url:
            error_detail = "Akamai URL未配置。无法生成reese84。"
            self.log_message(f"任务 {task_id}: {error_detail}", "red")
            return None, None, error_detail

        demo_module = None
        original_demo_module_session_proxies = None

        try:
            bestV8_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "bestV8")
            if bestV8_dir not in sys.path: sys.path.insert(0, bestV8_dir)
            import demo as demo_module
            from demo import Reese84Resolve

            self.log_message(f"任务 {task_id}: 使用Challenge URL: {challenge_url} 调用Reese84Resolve", "info")

            if hasattr(demo_module, 'session'):
                original_demo_module_session_proxies = demo_module.session.proxies.copy()
                if proxy_to_use_for_reese is not None and isinstance(proxy_to_use_for_reese, dict):
                    demo_module.session.proxies = proxy_to_use_for_reese
                    self.log_message(f"任务 {task_id}: 已临时为 demo_module.session 设置代理: {proxy_to_use_for_reese.get('http')}", "debug")
                else:
                    demo_module.session.proxies = {}
                    self.log_message(f"任务 {task_id}: 无特定代理或空代理传递给 reese84 生成，demo_module.session 代理已清空。", "debug")

                resolver = Reese84Resolve(src=challenge_url) # Pass the configured session
            else:
                self.log_message(f"任务 {task_id}: demo.py 模块没有 'session' 对象。将创建独立的Reese84Resolve实例。", "yellow")
                resolver = Reese84Resolve(src=challenge_url)

            # 最后一次检查任务是否应该停止（resolver调用前）
            if task_obj and hasattr(task_obj, 'high_concurrency_crawler') and task_obj.high_concurrency_crawler:
                if not task_obj.high_concurrency_crawler.running:
                    error_detail = "任务已停止，取消reese84生成（resolver调用前）。"
                    self.log_message(f"任务 {task_id}: {error_detail}", "yellow")
                    return None, None, error_detail

            # Pass proxy_to_use to resolve method
            resolve_result = resolver.resolve(proxy_to_use=proxy_to_use_for_reese)

            error_detail = None # Assume success if resolve_result is obtained

            if resolve_result and resolve_result.get('status'):
                reese84_token = resolve_result.get("data", {}).get("token")
                ua_from_reese_resolver = resolve_result.get('ua')
                if reese84_token:
                    token_log_display = f"{reese84_token[:10]}...{reese84_token[-5:]}" if len(reese84_token) > 15 else reese84_token
                    self.log_message(f"任务 {task_id}: reese84 生成成功: {token_log_display} (UA: {ua_from_reese_resolver[:30]}...)", "green")
                else:
                    error_detail = f"reese84 响应成功但未找到token. Data: {resolve_result.get('data')}"
                    self.log_message(f"任务 {task_id}: {error_detail}", "yellow")
                    reese84_token = None # Ensure token is None if not found
            elif resolve_result: # Status is False or missing
                error_detail = f"reese84 生成失败. Status: {resolve_result.get('status', 'N/A')}, Data: {str(resolve_result.get('data'))[:200]}..."
                self.log_message(f"任务 {task_id}: {error_detail}", "red")
                ua_from_reese_resolver = resolve_result.get('ua') # Attempt to get UA even on failure
                reese84_token = None
            else: # resolve_result itself is None or empty
                error_detail = "Reese84Resolve.resolve() 返回空结果。"
                self.log_message(f"任务 {task_id}: {error_detail}", "red")
                reese84_token = None
                ua_from_reese_resolver = None

        except ImportError as e_imp:
            error_detail = f"导入 Reese84Resolve/demo 模块失败: {e_imp}"
            self.log_message(f"任务 {task_id}: {error_detail}", "red", True)
            reese84_token, ua_from_reese_resolver = None, None
        except FileNotFoundError as e_fnf:
            error_detail = f"Reese84Resolve 依赖文件未找到: {e_fnf}"
            self.log_message(f"任务 {task_id}: {error_detail}", "red", True)
            reese84_token, ua_from_reese_resolver = None, None
        except Exception as e_reese:
            error_detail = f"生成 reese84 时异常: {e_reese}"
            self.log_message(f"任务 {task_id}: {error_detail}", "red", True)
            reese84_token, ua_from_reese_resolver = None, None
        finally:
            if hasattr(demo_module, 'session') and original_demo_module_session_proxies is not None:
                demo_module.session.proxies = original_demo_module_session_proxies
                self.log_message(f"任务 {task_id}: 已恢复 demo_module.session 的原始代理: {original_demo_module_session_proxies.get('http')}", "debug")

        return reese84_token, ua_from_reese_resolver, error_detail

    def _extract_and_display_pnr_for_task(self, task_obj, gql_purchase_response, award_disp, cash_disp, curr_disp, pax_summary_info):
        try:
            pnr = None
            purchase_data = gql_purchase_response.get("data", {})
            if gql_purchase_response.get("errors"):
                err_msg = f"支付确认GQL错误: {gql_purchase_response['errors'][0].get('message','未知')}"
                task_obj.log_task_message(err_msg); task_obj.status="error_pnr_gql"; return

            purchase_orig_resp = purchase_data.get("bookingPurchase",{}).get("originalResponse",{})
            if purchase_orig_resp: # Check if purchase_orig_resp is not None
                pnr_keys = ["confirmationId","pnr","recordLocator","bookingReference"]
                for k in pnr_keys:
                    if k in purchase_orig_resp and purchase_orig_resp[k]:
                        pnr = str(purchase_orig_resp[k]); break
                if not pnr and isinstance(purchase_orig_resp.get("booking"),dict):
                    booking_data = purchase_orig_resp["booking"] # Assign to a variable first
                    for k_n in pnr_keys:
                        if k_n in booking_data and booking_data[k_n]:
                            pnr = str(booking_data[k_n]); break
            # After loops, check pnr
            if pnr:
                task_obj.status = "completed_booked"; task_obj.log_task_message(f"成功提取PNR: {pnr}")
                summary_title=f"任务 {task_obj.id} 预订成功"; summary=[f"=== {summary_title} ===", f"PNR: {pnr}","乘客:"]
                for i,p_s in enumerate(pax_summary_info): summary.append(f"{i+1}. {p_s.get('last_name','').upper()}/{p_s.get('first_name','').upper()}")
                card_mask = task_obj.params.get("payment_info", {}).get("card_number", "****")[:4]+"****"+task_obj.params.get("payment_info", {}).get("card_number", "********")[-4:]
                summary.append(f"支付: {award_disp}积分 + {cash_disp} {curr_disp} (卡: {card_mask})")
                full_sum_str = "\n".join(summary); task_obj.log_task_message(f"预订摘要:\n{full_sum_str}"); pyperclip.copy(full_sum_str)
                messagebox.showinfo(summary_title,full_sum_str)
            else:
                task_obj.status = "booked_pnr_missing"; task_obj.log_task_message("支付成功但未提取PNR")
        except Exception as e:
            task_obj.log_task_message(f"提取PNR信息出错:{e}",True); task_obj.status="error_pnr_exception"
        finally:
            self.update_task_list_ui()

    def log_message(self, msg, color_name=None, include_traceback=False):
        log_item = {"msg": msg, "color": color_name, "traceback": None}
        if include_traceback:
            import traceback # Only if not globally imported and strictly needed here
            log_item["traceback"] = traceback.format_exc() if sys.exc_info()[0] else "No exception for traceback."
        if current_thread() is main_thread() and hasattr(self, 'log_text') and self.log_text and self.log_text.winfo_exists():
            self._log_update_main_ui(log_item["msg"], log_item["color"])
            if log_item["traceback"] and log_item["traceback"] != "No exception for traceback.":
                self._log_update_main_ui(log_item["traceback"], "error")
        elif hasattr(self, 'log_queue'):
            self.log_queue.put(log_item)
        else:
            print(f"(LOG_FALLBACK_NO_QUEUE) {msg}")
            if log_item["traceback"] and log_item["traceback"] != "No exception for traceback.": print(log_item["traceback"])

    def process_log_queue(self):
        try:
            while not self.log_queue.empty():
                log_item = self.log_queue.get_nowait()
                if hasattr(self, 'log_text') and self.log_text and self.log_text.winfo_exists():
                    self._log_update_main_ui(log_item["msg"], log_item["color"])
                    if log_item["traceback"] and log_item["traceback"] != "No exception for traceback.":
                        self._log_update_main_ui(log_item["traceback"], "error")
                else:
                    print(f"(LOG_QUEUE_TO_CONSOLE) {log_item['msg']}")
                    if log_item["traceback"] and log_item["traceback"] != "No exception for traceback.": print(log_item["traceback"])
        except queue.Empty: pass
        except Exception as e:
            print(f"Error processing log queue: {e}")
        finally:
            if hasattr(self, 'root') and self.root.winfo_exists():
                 self.root.after(100, self.process_log_queue)

    def _log_update_main_ui(self, msg, color_name=None):
        if not (hasattr(self, 'log_text') and self.log_text and self.log_text.winfo_exists()):
            return
        current_time = datetime.now().strftime("%H:%M:%S"); log_line = f"[{current_time}] {msg}\n"
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_line)
        if color_name:
            tag = f"main_log_{color_name}"
            if tag not in self.log_text.tag_names():
                color_map = {"green": "#00AA00", "red": "#CC0000", "yellow": "#B8860B", "blue": "#0000FF"}
                self.log_text.tag_configure(tag, foreground=color_map.get(color_name, "black"))
            try:
                start_index = self.log_text.index(f"end - {len(log_line) +0}c linestart")
                end_index = self.log_text.index("end - 1l lineend")
                self.log_text.tag_add(tag, start_index, end_index)
            except tk.TclError: pass
        self.log_text.see(tk.END); self.log_text.config(state=tk.DISABLED)

    def save_debug_data(self, data, file_name_prefix="debug_data"):
        if not self.debug_mode: return
        try:
            debug_dir = "debug_logs"; os.makedirs(debug_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join(debug_dir, f"{file_name_prefix}_{timestamp}.json")
            with open(file_path, "w", encoding="utf-8") as f: json.dump(data, f, ensure_ascii=False, indent=2)
            self.log_message(f"[调试] 数据已保存: {file_path}", "blue"); return file_path
        except Exception as e: self.log_message(f"[!] 保存调试数据失败: {e}", "red"); return None

    def clear_formatted_input(self):
        if hasattr(self, 'formatted_input_text') and self.formatted_input_text:
            self.formatted_input_text.delete("1.0", tk.END)
            self.log_message("格式化输入已清空。", "blue")

    def fill_example_data(self):
        if hasattr(self, 'formatted_input_text') and self.formatted_input_text:
            example_text = (
                "JFK-HND-20250529-NH109-Business\n"  # 行程
                "1244450451/Mhuwwodg30\n"  # 账号密码
                "5484230000668399\n"  # 信用卡号
                "03/2030\n"  # 有效期
                "777\n"  # 安全码
                "AN/YIWEI\n"  # 用户姓/名
                "MALE\n"  # 性别
                "2002-04-10\n"  # 生日
                "\n" # 多人情况用空行隔开，下面是第二个乘客信息，如果单人则不需要
                # "ZHANG/QI\n" # 乘客2 姓/名
                # "FEMALE\n" # 乘客2 性别
                # "1998-02-02\n" # 乘客2 生日
            )
            self.formatted_input_text.delete("1.0", tk.END)
            self.formatted_input_text.insert("1.0", example_text)
            self.log_message("已填充示例数据到格式化输入框。", "blue")

    def _parse_formatted_input_for_task(self):
        if not (hasattr(self, 'formatted_input_text') and self.formatted_input_text):
            self.log_message("错误: formatted_input_text UI元素未初始化。", "red"); return None
        input_str = self.formatted_input_text.get("1.0", tk.END).strip()
        if not input_str: self.log_message("输入为空。", "yellow"); return None

        parsed_params = {"passengers_info": [], "auto_start_crawler": False, "auto_book_on_find": True, "use_points_for_tax": self.use_points_for_tax_var.get()}
        lines = [line.strip() for line in input_str.split('\n') if line.strip()] # 移除空行

        if not lines or len(lines) < 8: # 基本信息至少需要8行
            self.log_message(f"输入格式错误: 基本信息不足8行。收到 {len(lines)} 行。", "red")
            return None

        try:
            # 1. 行程
            trip_parts = lines[0].split('-')
            if len(trip_parts) < 5: #确保至少有 Origin-Dest-Date-Flight-Cabin 五个部分
                self.log_message(f"行程格式错误 (第1行): {lines[0]}。至少需要5个部分，例如: SEA-HND-20250604-NH117-Business", "red")
                return None
            parsed_params["origin"] = trip_parts[0].upper()
            parsed_params["destination"] = trip_parts[1].upper() # Destination is the second part
            parsed_params["date_str"] = trip_parts[2]          # Date is the third part
            parsed_params["flight_no_str"] = trip_parts[3]    # Flight number(s) is the fourth part
            parsed_params["cabin"] = trip_parts[4]             # Cabin is the fifth part

            # 2. 账号密码
            if '/' in lines[1]:
                acc_user, acc_pass = lines[1].split('/', 1)
                parsed_params["credentials"] = {"username": acc_user, "password": acc_pass}
            else:
                self.log_message(f"账号密码格式错误 (第2行): {lines[1]}。应为 账号/密码", "red")
                return None

            # 3. 信用卡号
            parsed_params.setdefault("payment_info", {})["card_number"] = lines[2].replace(" ", "")

            # 4. 有效期
            if '/' in lines[3]:
                exp_month, exp_year = lines[3].split('/',1)
                parsed_params.setdefault("payment_info", {})["expiry_month"] = exp_month.strip()
                parsed_params.setdefault("payment_info", {})["expiry_year"] = exp_year.strip()
                parsed_params.setdefault("payment_info", {})["expiry_full"] = lines[3] # 保留完整格式
            else:
                self.log_message(f"信用卡有效期格式错误 (第4行): {lines[3]}。应为 MM/YYYY", "red")
                return None

            # 5. 安全码
            parsed_params.setdefault("payment_info", {})["cvv"] = lines[4]

            # 处理乘客信息
            passenger_lines_start_index = 5
            num_passenger_info_lines = 3 # 每位乘客固定3行信息：姓名，性别，生日

            current_line_index = passenger_lines_start_index
            while current_line_index + num_passenger_info_lines -1 < len(lines):
                pax_info = {}
                # 姓/名
                name_parts = lines[current_line_index].split('/')
                if len(name_parts) != 2:
                    self.log_message(f"乘客姓名格式错误 (第 {current_line_index + 1} 行): {lines[current_line_index]}。应为 姓/名", "red")
                    return None
                pax_info["last_name"] = name_parts[0].strip()
                pax_info["first_name"] = name_parts[1].strip()
                current_line_index += 1

                # 性别
                gender_str = lines[current_line_index].upper()
                if gender_str not in ["MALE", "FEMALE"]:
                    self.log_message(f"乘客性别格式错误 (第 {current_line_index + 1} 行): {lines[current_line_index]}。应为 MALE 或 FEMALE", "red")
                    return None
                pax_info["gender"] = gender_str
                pax_info["title"] = "MR" if gender_str == "MALE" else "MS" # 默认 MS，可根据需要调整
                current_line_index += 1

                # 生日
                # 验证生日格式 YYYY-MM-DD
                try:
                    datetime.strptime(lines[current_line_index], '%Y-%m-%d')
                    pax_info["dob"] = lines[current_line_index]
                except ValueError:
                    self.log_message(f"乘客生日格式错误 (第 {current_line_index + 1} 行): {lines[current_line_index]}。应为 YYYY-MM-DD", "red")
                    return None
                current_line_index += 1

                parsed_params["passengers_info"].append(pax_info)

            if not parsed_params["passengers_info"]:
                self.log_message("未解析到任何乘客信息。", "red")
                return None

            # 默认账单地址（如果需要，可以后续扩展为从输入读取）
            # 根据用户之前的UI设计，账单地址似乎不是从这个粘贴板输入的，而是全局配置或后续步骤填写
            # 这里可以设置一个默认的或者标记为需要后续填写
            parsed_params["billing_address"] = {
                "line1": "Default Address",
                "city": "Default City",
                "state": "XX",
                "postal_code": "0000",
                "country_code": "AU"
            }
            # 可以在后续步骤中提示用户检查或修改账单地址，或者提供专门的UI输入
            self.log_message("使用了默认账单地址，请在出票前确认。", "yellow")

        except IndexError:
            self.log_message(f"输入行数不足，解析到第 {current_line_index +1} 行时出错。请检查输入格式和行数。", "red")
            return None
        except Exception as e_parse:
            self.log_message(f"解析输入时发生意外错误: {e_parse}。请检查格式。", "red", True)
            return None

        # Validate required fields before returning
        required_task_fields = ["origin", "destination", "date_str", "flight_no_str", "cabin"]
        if not all(parsed_params.get(f) for f in required_task_fields):
            self.log_message("输入解析错误: 缺少基本行程信息 (O/D/Date/Flights/Cabin)。", "red"); return None
        if not parsed_params["passengers_info"]:
            self.log_message("输入解析错误: 至少需要一名乘客信息。", "red"); return None

        payment_info = parsed_params.get("payment_info", {})
        required_payment_keys = ["card_number", "expiry_month", "expiry_year", "cvv"]
        if not all(payment_info.get(pk) for pk in required_payment_keys):
            self.log_message("输入解析错误: 支付信息不完整 (卡号/有效期/CVV)。", "red"); return None

        # auto_start_crawler, auto_book_on_find, use_points_for_tax 维持默认值或从UI获取
        # 这些参数在新格式中没有明确指定，可以考虑在UI上提供勾选框

        self.log_message("格式化输入解析成功。", "green")
        return parsed_params

    def _construct_target_url_for_task(self, task_obj):
        base_url = "https://book.virginaustralia.com/dx/VADX/#/flight-selection"
        params = task_obj.params
        # Default values
        adt = "1" # Assuming 1 adult, can be made dynamic if needed
        award_booking = "true"
        pos = "au-en"
        channel = ""
        journey_type = "one-way"

        # Extract from task_obj.params
        origin = params.get("origin")
        destination = params.get("destination")
        date_str = params.get("date_str") # Expected format YYYYMMDD from parser
        cabin = params.get("cabin", "Business") # Default to Business if not specified

        if not all([origin, destination, date_str]):
            self.log_message(f"任务 {task_obj.id}: 构建URL失败，缺少O/D或日期。", "red")
            return None

        # Convert cabin name to the format expected by the URL (e.g., Business, Economy, First)
        # The example URL uses 'First', but your input uses 'Business'. Assuming a direct mapping for now.
        cabin_url_param = cabin.capitalize() # Or a mapping if names differ significantly
        if cabin.lower() == "economy":
            cabin_url_param = "Economy"
        elif cabin.lower() == "business":
            cabin_url_param = "Business" # Example uses First, let's stick to what user provides
        elif cabin.lower() == "first":
            cabin_url_param = "First"

        # Format date for activeMonth (MM-DD-YYYY) and date (MM-DD-YYYY)
        try:
            dt_obj = datetime.strptime(date_str, "%Y%m%d") # Changed format to YYYYMMDD
            active_month_formatted = dt_obj.strftime("%m-%d-%Y")
            date_formatted = dt_obj.strftime("%m-%d-%Y")
        except ValueError:
            self.log_message(f"任务 {task_obj.id}: 日期格式无效 ({date_str})，应为 YYYYMMDD。", "red")
            return None

        query_params = {
            "ADT": adt,
            "class": cabin_url_param,
            "awardBooking": award_booking,
            "pos": pos,
            "channel": channel,
            "activeMonth": active_month_formatted,
            "journeyType": journey_type,
            "date": date_formatted,
            "origin": origin,
            "destination": destination
        }

        query_string = urllib.parse.urlencode(query_params)
        full_url = f"{base_url}?{query_string}"
        self.log_message(f"任务 {task_obj.id}: 构建Cookie获取URL: {full_url}", "debug")
        return full_url

    def request_cookie_for_task(self, task_id):
        task_obj = self.get_task_by_id(task_id)
        if not task_obj: self.log_message(f"请求Cookie失败: 任务 {task_id} 未找到。", "red"); return
        self.log_message(f"任务 {task_id}: 将Cookie请求加入队列。", "blue")
        # Ensure this is thread-safe if called from non-asyncio thread, which it is from add_new_task_from_input
        self.main_loop.call_soon_threadsafe(asyncio.create_task, self.cookie_request_queue.put(task_id))
        task_obj.status = "queued_for_cookie"; self.update_task_list_ui()

    def update_task_list_ui(self):
        if not (hasattr(self, 'scrollable_frame_tasks') and self.scrollable_frame_tasks and self.scrollable_frame_tasks.winfo_exists()):
             return # UI not ready
        try:
            for widget in self.scrollable_frame_tasks.winfo_children(): widget.destroy()
            if not self.tasks:
                ttk.Label(self.scrollable_frame_tasks, text="无任务").pack(padx=5, pady=5); return

            for task_obj in self.tasks:
                task_frame = ttk.Frame(self.scrollable_frame_tasks, borderwidth=1, relief="solid", padding=5)
                task_frame.pack(fill="x", expand=True, padx=5, pady=3)

                pax_summary = ", ".join([f"{p.get('last_name','').upper()}/{p.get('first_name','').upper()[:1]}." for p in task_obj.params.get("passengers_info",[])])
                pax_summary = pax_summary if pax_summary else "N/A"
                account_info = task_obj.params.get("credentials",{}).get("username","无账号")[:10] # Truncate for display

                line1_text = f"任务 {task_obj.id}: {task_obj.params.get('origin','?')}->{task_obj.params.get('destination','?')} ({task_obj.params.get('date_str','?')}) - {task_obj.params.get('flight_no_str','?')}"
                line2_text = f"乘客: {pax_summary} | 账号: {account_info} | 状态: {task_obj.status}"

                ttk.Label(task_frame, text=line1_text, anchor="w").pack(fill="x")
                ttk.Label(task_frame, text=line2_text, anchor="w").pack(fill="x")

                button_sub_frame = ttk.Frame(task_frame); button_sub_frame.pack(fill="x", pady=(3,0))
                if not task_obj.crawler_running and task_obj.status not in ["pending_browser_cookie", "pending_cookie_refresh"]:
                    ttk.Button(button_sub_frame, text="开始挂单", command=lambda t_id=task_obj.id: self.start_task_crawler(t_id), width=10).pack(side=tk.LEFT, padx=2)
                if task_obj.crawler_running:
                    ttk.Button(button_sub_frame, text="停止挂单", command=lambda t_id=task_obj.id: self.stop_task_crawler(t_id), width=10).pack(side=tk.LEFT, padx=2)
                if task_obj.status not in ["pending_browser_cookie", "pending_cookie_refresh"]:
                     ttk.Button(button_sub_frame, text="刷新Cookie", command=lambda t_id=task_obj.id: self.request_cookie_for_task(t_id), width=10).pack(side=tk.LEFT, padx=2)
                ttk.Button(button_sub_frame, text="任务日志", command=lambda t_obj=task_obj: self.show_task_log_window(t_obj), width=10).pack(side=tk.LEFT, padx=2)
                ttk.Button(button_sub_frame, text="删除任务", command=lambda t_id=task_obj.id: self.remove_task(t_id), width=10).pack(side=tk.RIGHT, padx=2)
        except tk.TclError as e: self.log_message(f"UI更新错误 (TclError): {e}", "red") # Catch if UI not fully ready
        except Exception as e_ui: self.log_message(f"UI更新时意外错误: {e_ui}", "red", True)
        finally: # Ensure canvas scrollregion is updated if it exists
            if hasattr(self, 'task_list_canvas') and self.task_list_canvas and self.task_list_canvas.winfo_exists():
                self.task_list_canvas.update_idletasks() # Process pending geometry changes
                self.task_list_canvas.config(scrollregion=self.task_list_canvas.bbox("all"))

    def remove_task(self, task_id_to_remove):
        task_obj = self.get_task_by_id(task_id_to_remove)
        if task_obj and task_obj.crawler_running and task_obj.high_concurrency_crawler:
            task_obj.high_concurrency_crawler.stop()
            # Consider waiting briefly for thread to join if stop is not immediate or if issues arise
        self.tasks = [t for t in self.tasks if t.id != task_id_to_remove]
        self.log_message(f"任务 {task_id_to_remove} 已移除。", "blue")
        self.update_task_list_ui()
        # Also remove from Redis if exists (optional, depends on desired behavior)
        r_client_del = get_redis_client()
        if r_client_del:
            try: r_client_del.delete(get_task_cookie_redis_key(task_id_to_remove))
            except: pass # Ignore errors for this optional cleanup

    def show_task_log_window(self, task_obj):
        log_window = tk.Toplevel(self.root)
        log_window.title(f"任务 {task_obj.id} 日志 ({task_obj.params.get('origin','?')}-{task_obj.params.get('destination','?')})")
        log_window.geometry("600x400")
        log_text_widget = tk.Text(log_window, wrap=tk.WORD, state=tk.DISABLED, height=20, width=70)
        log_text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar = ttk.Scrollbar(log_window, command=log_text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        log_text_widget['yscrollcommand'] = scrollbar.set

        log_text_widget.config(state=tk.NORMAL)
        for entry in task_obj.log_messages: log_text_widget.insert(tk.END, entry + "\n")
        log_text_widget.see(tk.END); log_text_widget.config(state=tk.DISABLED)

        # Optional: Button to refresh logs in this window, or auto-refresh (more complex)
        ttk.Button(log_window, text="关闭", command=log_window.destroy).pack(pady=5)
        log_window.transient(self.root); log_window.grab_set() # Make it modal

    def stop_task_crawler(self, task_id):
        task_obj = self.get_task_by_id(task_id)
        if task_obj and task_obj.crawler_running and task_obj.high_concurrency_crawler:
            task_obj.log_task_message("TMW: 收到停止挂单指令。")
            task_obj.high_concurrency_crawler.stop() # This should set internal running flag to False
            task_obj.crawler_running = False # Reflect immediately in UI
            task_obj.status = "stopped_manual"
            # Note: Crawler thread might take a moment to fully exit.
            # We are not joining thread here to avoid blocking UI.
        else: self.log_message(f"任务 {task_id} 爬虫未运行或不存在。", "yellow")
        self.update_task_list_ui()

if __name__ == "__main__":
    root_tk = tk.Tk()
    try: loop = asyncio.get_event_loop()
    except RuntimeError: loop = asyncio.new_event_loop(); asyncio.set_event_loop(loop)
    app_instance = RedeemApp(root_tk)
    def on_closing_main_window():
        if hasattr(app_instance, 'tasks'):
            for task_item in app_instance.tasks:
                if task_item.crawler_running and task_item.high_concurrency_crawler: task_item.high_concurrency_crawler.stop()
        root_tk.destroy()
    root_tk.protocol("WM_DELETE_WINDOW", on_closing_main_window)
    async def tkinter_loop(root, app, interval=0.05):
        if hasattr(app, 'root') and app.root.winfo_exists():
             app.root.after(100, app.process_log_queue) # Start log processor once loop is about to run
        else:
            print("[CRITICAL] tkinter_loop: Root window not valid for starting log processor at loop start.")
        while True:
            try:
                root.update()
                await asyncio.sleep(interval)
            except tk.TclError: break
            except Exception as e_async_tk: print(f"Async Tk loop error: {e_async_tk}"); break
    try:
        loop.run_until_complete(tkinter_loop(root_tk, app_instance))
    except KeyboardInterrupt: print("[*] Program interrupted.")
    finally:
        if not loop.is_closed():
            pending_tasks = [task_to_cancel for task_to_cancel in asyncio.all_tasks(loop=loop) if task_to_cancel is not asyncio.current_task()]
            if pending_tasks:
                for task_to_cancel_item in pending_tasks: task_to_cancel_item.cancel()
                try: loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                except RuntimeError as e_gather: print(f"Error during gather on close: {e_gather}")
            try: loop.close()
            except RuntimeError as e_close: print(f"Error closing loop: {e_close}")
        print("[*] Program exited.")
