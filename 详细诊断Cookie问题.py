#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细诊断Cookie获取问题
分析扩展通信失败的具体原因
"""

import asyncio
import websockets
import json
from datetime import datetime


class DetailedDiagnostic:
    def __init__(self):
        self.messages = []
        self.errors = []
        self.communication_timeline = []
        self.script_injection_status = None
        self.ping_test_result = None
        self.cookie_data_received = False
    
    def log_event(self, event_type, message, details=None):
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        event = {
            "timestamp": timestamp,
            "type": event_type,
            "message": message,
            "details": details
        }
        self.communication_timeline.append(event)
        print(f"[{timestamp}] {event_type.upper()}: {message}")
        if details:
            print(f"    详情: {details}")
    
    async def handle_websocket_message(self, websocket, message):
        try:
            data = json.loads(message)
            message_type = data.get("type", "unknown")
            
            self.messages.append(data)
            
            if message_type == "status_update":
                await self.handle_status_update(data)
            elif message_type == "cookie_data":
                await self.handle_cookie_data(data)
            
            # 发送确认响应
            await websocket.send(json.dumps({
                "status": "received",
                "message": f"{message_type} 已收到"
            }))
            
        except json.JSONDecodeError:
            self.log_event("error", "收到无效JSON消息", message[:100])
        except Exception as e:
            self.log_event("error", f"处理消息失败: {e}")
    
    async def handle_status_update(self, data):
        message = data.get("message", "")
        error = data.get("error", "")
        
        if error:
            self.errors.append(error)
            self.log_event("error", error)
            
            # 分析特定错误
            if "Could not establish connection" in error:
                self.log_event("analysis", "检测到通信连接失败错误")
                self.log_event("suggestion", "可能原因: 内容脚本消息监听器未正确设置")
            elif "内容脚本通信失败" in error:
                self.log_event("analysis", "内容脚本通信完全失败")
                self.log_event("suggestion", "建议检查: 1) 脚本注入 2) API兼容性 3) 消息监听器")
            elif "注入内容脚本失败" in error:
                self.script_injection_status = "failed"
                self.log_event("analysis", "脚本注入失败")
        else:
            self.log_event("info", message)
            
            # 分析状态消息
            if "Content script injected successfully" in message:
                self.script_injection_status = "success"
                self.log_event("analysis", "脚本注入成功")
            elif "Ping test successful" in message:
                self.ping_test_result = "success"
                self.log_event("analysis", "Ping测试成功 - 通信正常")
            elif "Ping test failed" in message:
                self.ping_test_result = "failed"
                self.log_event("analysis", "Ping测试失败 - 通信异常")
            elif "cs_ready" in message:
                self.log_event("analysis", "内容脚本准备就绪信号")
    
    async def handle_cookie_data(self, data):
        self.cookie_data_received = True
        payload = data.get("payload", [])
        execution_id = data.get("execution_id", "")
        
        self.log_event("success", f"收到Cookie数据: {len(payload)} 个")
        
        if execution_id:
            self.log_event("info", f"ExecutionID: {execution_id[:20]}...")
        else:
            self.log_event("warning", "缺少ExecutionID")
        
        # 分析重要Cookie
        important_cookies = []
        for cookie in payload:
            if isinstance(cookie, dict):
                name = cookie.get("name", "")
                if name in ["DCSESSIONID", "IBEOpenToken", "reese84"]:
                    important_cookies.append(name)
        
        if important_cookies:
            self.log_event("success", f"找到重要Cookie: {', '.join(important_cookies)}")
        else:
            self.log_event("warning", "未找到重要Cookie")
    
    def analyze_communication_pattern(self):
        """分析通信模式"""
        self.log_event("analysis", "开始分析通信模式")
        
        # 检查脚本注入
        if self.script_injection_status == "success":
            self.log_event("check", "✅ 脚本注入: 成功")
        elif self.script_injection_status == "failed":
            self.log_event("check", "❌ 脚本注入: 失败")
        else:
            self.log_event("check", "⚠️  脚本注入: 状态未知")
        
        # 检查ping测试
        if self.ping_test_result == "success":
            self.log_event("check", "✅ Ping测试: 成功")
        elif self.ping_test_result == "failed":
            self.log_event("check", "❌ Ping测试: 失败")
        else:
            self.log_event("check", "⚠️  Ping测试: 未执行")
        
        # 检查Cookie获取
        if self.cookie_data_received:
            self.log_event("check", "✅ Cookie获取: 成功")
        else:
            self.log_event("check", "❌ Cookie获取: 失败")
        
        # 错误统计
        error_count = len(self.errors)
        if error_count == 0:
            self.log_event("check", "✅ 错误数量: 0")
        else:
            self.log_event("check", f"❌ 错误数量: {error_count}")
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "="*60)
        print("📋 详细诊断报告")
        print("="*60)
        
        # 基本统计
        print(f"总消息数: {len(self.messages)}")
        print(f"错误数量: {len(self.errors)}")
        print(f"通信事件: {len(self.communication_timeline)}")
        
        # 关键状态
        print(f"\n🔍 关键状态检查:")
        print(f"脚本注入: {self.script_injection_status or '未知'}")
        print(f"Ping测试: {self.ping_test_result or '未执行'}")
        print(f"Cookie获取: {'成功' if self.cookie_data_received else '失败'}")
        
        # 错误分析
        if self.errors:
            print(f"\n❌ 错误详情:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        # 诊断结论
        print(f"\n🎯 诊断结论:")
        
        if self.cookie_data_received:
            print("✅ Cookie获取功能正常工作")
        elif self.script_injection_status == "failed":
            print("❌ 脚本注入失败 - 这是主要问题")
            print("💡 建议: 检查扩展权限和manifest.json配置")
        elif self.ping_test_result == "failed":
            print("❌ 通信测试失败 - API兼容性问题")
            print("💡 建议: 检查browser vs chrome API使用")
        elif len(self.errors) > 0:
            print("❌ 存在通信错误")
            print("💡 建议: 查看具体错误信息并修复")
        else:
            print("⚠️  状态不明确，需要更多信息")
        
        # 修复建议
        print(f"\n🔧 修复建议:")
        if not self.cookie_data_received:
            print("1. 重新加载浏览器扩展")
            print("2. 检查Firefox控制台错误信息")
            print("3. 确认Virgin Australia网站已登录")
            print("4. 验证扩展权限设置")
            print("5. 尝试手动点击扩展图标")


async def main():
    print("🔬 详细诊断Cookie获取问题")
    print("="*60)
    print("此程序将详细分析扩展通信失败的原因")
    print("请确保:")
    print("1. Firefox浏览器已打开")
    print("2. Virgin Australia扩展已安装并启用")
    print("3. 访问Virgin Australia网站")
    print("="*60)
    
    diagnostic = DetailedDiagnostic()
    
    async def handle_client(websocket, path):
        diagnostic.log_event("connection", f"扩展已连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                await diagnostic.handle_websocket_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            diagnostic.log_event("connection", "扩展断开连接")
        except Exception as e:
            diagnostic.log_event("error", f"处理连接时出错: {e}")
    
    try:
        print("🚀 启动诊断服务器 (端口: 8765)...")
        async with websockets.serve(handle_client, "127.0.0.1", 8765):
            print("✅ 诊断服务器运行中")
            print("⏰ 将运行45秒进行详细诊断...")
            
            # 运行45秒进行诊断
            await asyncio.sleep(45)
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断诊断")
    except Exception as e:
        print(f"❌ 诊断服务器出错: {e}")
    finally:
        # 分析通信模式
        diagnostic.analyze_communication_pattern()
        
        # 生成诊断报告
        diagnostic.generate_diagnosis_report()


if __name__ == "__main__":
    asyncio.run(main())
