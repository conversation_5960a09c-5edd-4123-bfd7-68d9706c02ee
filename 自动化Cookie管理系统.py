#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化Cookie管理系统
专门用于自动化出票程序的Cookie获取和管理
"""

import asyncio
import websockets
import json
import time
from datetime import datetime, timedelta
import threading
import queue


class AutomatedCookieManager:
    def __init__(self):
        self.cookie_data = None
        self.cookie_expiry_time = None
        self.cookie_refresh_interval = 14 * 60  # 14分钟
        self.last_cookie_update = None
        self.websocket_server = None
        self.connected_extensions = set()
        self.cookie_queue = queue.Queue()
        self.is_running = False
        self.refresh_timer = None
        
    def start_cookie_manager(self):
        """启动Cookie管理系统"""
        print("🚀 启动自动化Cookie管理系统")
        print("="*50)
        
        self.is_running = True
        
        # 启动WebSocket服务器
        asyncio.run(self._start_websocket_server())
    
    async def _start_websocket_server(self):
        """启动WebSocket服务器"""
        print("📡 启动WebSocket服务器 (端口: 8765)")
        
        async def handle_extension(websocket, path):
            await self._handle_extension_connection(websocket, path)
        
        try:
            async with websockets.serve(handle_extension, "127.0.0.1", 8765):
                print("✅ WebSocket服务器运行中")
                print("⏰ 等待浏览器扩展连接...")
                
                # 启动定时刷新任务
                refresh_task = asyncio.create_task(self._cookie_refresh_scheduler())
                
                # 启动Cookie监控任务
                monitor_task = asyncio.create_task(self._cookie_monitor())
                
                # 等待任务完成
                await asyncio.gather(refresh_task, monitor_task)
                
        except Exception as e:
            print(f"❌ WebSocket服务器启动失败: {e}")
    
    async def _handle_extension_connection(self, websocket, path):
        """处理扩展连接"""
        self.connected_extensions.add(websocket)
        client_addr = websocket.remote_address
        print(f"🔗 浏览器扩展已连接: {client_addr}")
        
        try:
            # 如果是首次连接，立即请求Cookie
            if not self.cookie_data:
                await self._request_cookie_from_extension(websocket)
            
            async for message in websocket:
                await self._process_extension_message(websocket, message)
                
        except websockets.exceptions.ConnectionClosed:
            print(f"📱 扩展断开连接: {client_addr}")
        except Exception as e:
            print(f"❌ 处理扩展连接时出错: {e}")
        finally:
            self.connected_extensions.discard(websocket)
    
    async def _process_extension_message(self, websocket, message):
        """处理扩展消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type", "unknown")
            
            if message_type == "cookie_data":
                await self._handle_cookie_data(data)
                await websocket.send(json.dumps({
                    "status": "success",
                    "message": "Cookie数据已收到并已开始调度处理"
                }))
                
            elif message_type == "status_update":
                await self._handle_status_update(data)
                await websocket.send(json.dumps({
                    "status": "received",
                    "message": "状态更新已收到"
                }))
            
        except json.JSONDecodeError:
            print(f"❌ 收到无效JSON消息: {message[:100]}...")
        except Exception as e:
            print(f"❌ 处理扩展消息时出错: {e}")
    
    async def _handle_cookie_data(self, data):
        """处理Cookie数据"""
        payload = data.get("payload", [])
        execution_id = data.get("execution_id", "")
        user_agent = data.get("user_agent", "")
        source = data.get("source", "unknown")
        
        print(f"🍪 收到Cookie数据 (来源: {source})")
        print(f"   Cookie数量: {len(payload)}")
        print(f"   ExecutionID: {'有' if execution_id else '无'}")
        
        # 检查重要Cookie
        important_cookies = []
        for cookie in payload:
            if isinstance(cookie, dict):
                name = cookie.get("name", "")
                if name in ["DCSESSIONID", "IBEOpenToken", "reese84"]:
                    important_cookies.append(name)
        
        if important_cookies:
            print(f"   🎯 重要Cookie: {', '.join(important_cookies)}")
        else:
            print(f"   ⚠️  未找到重要Cookie")
        
        # 更新Cookie数据
        self.cookie_data = {
            "payload": payload,
            "execution_id": execution_id,
            "user_agent": user_agent,
            "source": source,
            "timestamp": datetime.now().isoformat(),
            "important_cookies": important_cookies
        }
        
        # 设置过期时间
        self.cookie_expiry_time = datetime.now() + timedelta(minutes=self.cookie_refresh_interval)
        self.last_cookie_update = datetime.now()
        
        print(f"✅ Cookie数据已更新，下次刷新时间: {self.cookie_expiry_time.strftime('%H:%M:%S')}")
        
        # 通知其他组件Cookie已更新
        self.cookie_queue.put(self.cookie_data)
    
    async def _handle_status_update(self, data):
        """处理状态更新"""
        message = data.get("message", "")
        error = data.get("error", "")
        
        if error:
            print(f"❌ 扩展错误: {error}")
        else:
            print(f"ℹ️  扩展状态: {message}")
    
    async def _request_cookie_from_extension(self, websocket):
        """向扩展请求Cookie"""
        request_command = {
            "action": "navigate_and_get_cookies",
            "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?ADT=1&class=Business",
            "source": "automated_refresh"
        }
        
        try:
            await websocket.send(json.dumps(request_command))
            print("📤 已向扩展发送Cookie获取请求")
        except Exception as e:
            print(f"❌ 发送Cookie请求失败: {e}")
    
    async def _cookie_refresh_scheduler(self):
        """Cookie刷新调度器"""
        while self.is_running:
            try:
                # 检查是否需要刷新Cookie
                if self._should_refresh_cookie():
                    print(f"⏰ Cookie即将过期，开始自动刷新...")
                    await self._refresh_cookie()
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
            except Exception as e:
                print(f"❌ Cookie刷新调度器出错: {e}")
                await asyncio.sleep(60)
    
    def _should_refresh_cookie(self):
        """检查是否需要刷新Cookie"""
        if not self.cookie_expiry_time:
            return True  # 没有Cookie，需要获取
        
        # 提前2分钟刷新
        refresh_threshold = self.cookie_expiry_time - timedelta(minutes=2)
        return datetime.now() >= refresh_threshold
    
    async def _refresh_cookie(self):
        """刷新Cookie"""
        if not self.connected_extensions:
            print("⚠️  没有连接的扩展，无法刷新Cookie")
            return
        
        # 向所有连接的扩展发送刷新请求
        for websocket in self.connected_extensions.copy():
            try:
                await self._request_cookie_from_extension(websocket)
                break  # 只需要一个扩展响应即可
            except Exception as e:
                print(f"❌ 向扩展发送刷新请求失败: {e}")
                self.connected_extensions.discard(websocket)
    
    async def _cookie_monitor(self):
        """Cookie监控任务"""
        while self.is_running:
            try:
                if self.cookie_data:
                    time_since_update = datetime.now() - self.last_cookie_update
                    time_until_expiry = self.cookie_expiry_time - datetime.now() if self.cookie_expiry_time else timedelta(0)
                    
                    print(f"📊 Cookie状态监控:")
                    print(f"   上次更新: {time_since_update}")
                    print(f"   距离过期: {time_until_expiry}")
                    print(f"   连接扩展: {len(self.connected_extensions)} 个")
                    print(f"   重要Cookie: {len(self.cookie_data.get('important_cookies', []))} 个")
                else:
                    print(f"📊 Cookie状态: 无Cookie数据")
                
                # 每5分钟报告一次状态
                await asyncio.sleep(300)
                
            except Exception as e:
                print(f"❌ Cookie监控出错: {e}")
                await asyncio.sleep(300)
    
    def get_current_cookie(self):
        """获取当前有效的Cookie"""
        if not self.cookie_data:
            return None
        
        # 检查Cookie是否过期
        if self.cookie_expiry_time and datetime.now() >= self.cookie_expiry_time:
            print("⚠️  Cookie已过期")
            return None
        
        return self.cookie_data
    
    def is_cookie_valid(self):
        """检查Cookie是否有效"""
        cookie = self.get_current_cookie()
        return cookie is not None and len(cookie.get('important_cookies', [])) > 0
    
    def stop_cookie_manager(self):
        """停止Cookie管理系统"""
        print("⏹️  停止Cookie管理系统")
        self.is_running = False


def main():
    """主函数"""
    print("🎯 自动化出票程序 - Cookie管理系统")
    print("="*60)
    print("此系统将自动管理Cookie的获取和刷新")
    print("适用于自动化出票程序，无需手动干预")
    print("Cookie有效期: 14-15分钟，自动提前2分钟刷新")
    print("="*60)
    
    manager = AutomatedCookieManager()
    
    try:
        # 启动Cookie管理系统
        manager.start_cookie_manager()
    except KeyboardInterrupt:
        print("\n⏹️  用户中断程序")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
    finally:
        manager.stop_cookie_manager()
        print("👋 程序已退出")


if __name__ == "__main__":
    main()
