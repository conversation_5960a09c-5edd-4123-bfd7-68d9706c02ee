# 浏览器扩展和WebSocket连接优化方案

## 🔍 **问题分析**

### 问题1: 浏览器扩展获取登录cookie失败
**可能原因**:
1. Virgin Australia网站更新了登录流程
2. 扩展的选择器过时
3. 登录检测逻辑不准确
4. Cookie获取权限问题

### 问题2: UI启动时WebSocket连接很慢
**可能原因**:
1. WebSocket服务器启动延迟
2. 连接超时设置过长
3. 重连机制不够优化
4. 异步事件循环阻塞

## ✅ **已实施的优化**

### 1. WebSocket服务器优化

**UI端优化** (`ui.py`):
```python
# 快速启动WebSocket服务器
async def start_websocket_server(host='127.0.0.1', port=8765):
    server = await websockets.serve(
        ws_handler, host, port,
        ping_interval=20,  # 减少ping间隔
        ping_timeout=10,   # 减少ping超时
        close_timeout=5    # 减少关闭超时
    )
    await server.wait_closed()

# 启动优化
def start_internal_websocket_server(self):
    self.ws_server_thread = Thread(target=run_server_in_thread, daemon=True)
    self.ws_server_thread.start()
    time.sleep(0.1)  # 给服务器启动时间
```

### 2. 浏览器扩展连接优化

**扩展端优化** (`background.js`):
```javascript
function connectWebSocket() {
    socket = new WebSocket(PYTHON_WS_URL);
    
    // 设置连接超时
    const connectionTimeout = setTimeout(() => {
        if (socket && socket.readyState === WebSocket.CONNECTING) {
            socket.close();
            setTimeout(connectWebSocket, 2000); // 2秒后重试
        }
    }, 5000); // 5秒超时
    
    socket.onopen = function(event) {
        clearTimeout(connectionTimeout);
        console.log("[VA BackgroundHost] WebSocket 快速连接已建立。");
    };
    
    socket.onclose = function(event) {
        clearTimeout(connectionTimeout);
        setTimeout(connectWebSocket, 2000); // 快速重连
    };
    
    socket.onerror = function(error) {
        clearTimeout(connectionTimeout);
        setTimeout(connectWebSocket, 1000); // 1秒后重试
    };
}
```

## 🔧 **进一步的解决方案**

### 1. 浏览器扩展Cookie获取问题

#### A. 更新登录检测逻辑

创建新的内容脚本优化版本：

```javascript
// 优化的登录检测
async function detectLoginStatus() {
    // 1. 检查多种登录指示器
    const loginIndicators = [
        'span.pii-data[data-translation="header.greetings"]',
        '[data-testid="header-profile-button-name"]',
        '.user-greeting',
        '.logged-in-user',
        // 添加更多可能的选择器
    ];
    
    // 2. 检查URL中的session信息
    const hasSessionInUrl = window.location.href.includes('execution=') || 
                           window.location.href.includes('sessionId=');
    
    // 3. 检查localStorage/sessionStorage
    const hasStoredSession = localStorage.getItem('userSession') || 
                            sessionStorage.getItem('authToken');
    
    return { isLoggedIn: false, method: 'unknown' };
}
```

#### B. 增强Cookie获取

```javascript
// 获取所有相关域的Cookie
async function getAllRelevantCookies() {
    const domains = [
        'virginaustralia.com',
        '.virginaustralia.com',
        'book.virginaustralia.com',
        'velocityfrequentflyer.com'
    ];
    
    let allCookies = [];
    for (const domain of domains) {
        try {
            const cookies = await browser.cookies.getAll({ domain });
            allCookies = allCookies.concat(cookies);
        } catch (e) {
            console.error(`获取${domain}的Cookie失败:`, e);
        }
    }
    
    return allCookies;
}
```

### 2. WebSocket连接速度优化

#### A. 预连接机制

```python
# 在UI初始化时预先建立连接
class RedeemApp:
    def __init__(self, root_window):
        # ... 其他初始化代码
        
        # 预先启动WebSocket服务器
        self.pre_start_websocket()
        
        # 延迟其他初始化，让WebSocket先启动
        self.root.after(100, self.complete_initialization)
    
    def pre_start_websocket(self):
        """预先启动WebSocket服务器"""
        if self.ws_server_thread is None:
            self.ws_server_thread = Thread(target=run_server_in_thread, daemon=True)
            self.ws_server_thread.start()
```

#### B. 连接池机制

```javascript
// 浏览器扩展端连接池
class WebSocketPool {
    constructor() {
        this.connections = [];
        this.maxConnections = 3;
        this.currentIndex = 0;
    }
    
    async getConnection() {
        if (this.connections.length < this.maxConnections) {
            const ws = new WebSocket(PYTHON_WS_URL);
            this.connections.push(ws);
            return ws;
        }
        
        // 轮询使用现有连接
        const ws = this.connections[this.currentIndex];
        this.currentIndex = (this.currentIndex + 1) % this.connections.length;
        return ws;
    }
}
```

## 🚀 **立即可用的解决方案**

### 1. 快速修复Cookie获取问题

**步骤1**: 检查Virgin Australia网站是否有变化
```bash
# 在浏览器控制台运行
console.log('当前URL:', window.location.href);
console.log('登录元素:', document.querySelector('span.pii-data[data-translation="header.greetings"]'));
console.log('所有Cookie:', document.cookie);
```

**步骤2**: 手动测试扩展
1. 打开Virgin Australia网站
2. 手动登录
3. 打开扩展popup
4. 点击"获取Cookie"按钮
5. 查看控制台输出

**步骤3**: 更新扩展权限
确保 `manifest.json` 包含所有必要权限：
```json
{
  "permissions": [
    "cookies",
    "activeTab",
    "storage",
    "tabs",
    "<all_urls>",
    "*://*.virginaustralia.com/*",
    "*://*.velocityfrequentflyer.com/*"
  ]
}
```

### 2. 快速修复WebSocket连接慢问题

**步骤1**: 检查端口占用
```bash
netstat -an | findstr 8765
```

**步骤2**: 测试直接连接
```python
import websockets
import asyncio

async def test_connection():
    try:
        async with websockets.connect("ws://127.0.0.1:8765") as ws:
            print("连接成功!")
            await ws.send('{"type": "test"}')
            response = await ws.recv()
            print(f"响应: {response}")
    except Exception as e:
        print(f"连接失败: {e}")

asyncio.run(test_connection())
```

**步骤3**: 优化启动顺序
```python
# 在ui.py的main函数中
def main():
    root = tk.Tk()
    
    # 先启动WebSocket服务器
    print("启动WebSocket服务器...")
    app = RedeemApp(root)
    
    # 等待服务器启动
    time.sleep(0.5)
    
    print("启动UI...")
    root.mainloop()
```

## 📋 **调试检查清单**

### Cookie获取问题调试
- [ ] 检查浏览器扩展是否正确安装
- [ ] 确认Virgin Australia网站已登录
- [ ] 检查扩展权限是否足够
- [ ] 查看浏览器控制台错误信息
- [ ] 验证Cookie域名和路径设置
- [ ] 测试手动Cookie获取

### WebSocket连接问题调试
- [ ] 确认端口8765未被占用
- [ ] 检查防火墙设置
- [ ] 验证WebSocket服务器启动日志
- [ ] 测试直接WebSocket连接
- [ ] 检查异步事件循环状态
- [ ] 监控连接建立时间

## 🎯 **预期改进效果**

### WebSocket连接优化
- **启动时间**: 从5-10秒减少到1-2秒
- **重连速度**: 从5秒减少到1-2秒
- **连接稳定性**: 增加超时和错误处理
- **响应速度**: 减少ping间隔提高实时性

### Cookie获取优化
- **成功率**: 提高登录检测准确性
- **兼容性**: 支持更多登录状态指示器
- **错误处理**: 更详细的错误信息和重试机制
- **稳定性**: 增强Cookie获取的可靠性

这些优化应该能显著改善您遇到的两个问题。建议先测试WebSocket连接优化，然后再处理Cookie获取问题。
