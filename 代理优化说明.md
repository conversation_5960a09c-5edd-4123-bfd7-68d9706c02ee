# 澳洲航空高频抓取代理优化方案

## 问题分析

原有的高频抓取系统在使用代理时出现以下问题：

1. **代理冲突**：reese84生成和API调用使用相同代理，导致冲突
2. **代理健康状态缺失**：没有代理健康检查机制，失败的代理继续被使用
3. **负载不均衡**：随机选择代理，没有考虑代理性能
4. **故障恢复缺失**：失败的代理没有冷却和恢复机制

## 优化方案

### 1. 代理管理器 (proxy_manager.py)

创建了专门的代理管理器，提供以下功能：

- **健康检查**：跟踪每个代理的成功/失败次数
- **冷却机制**：失败次数过多的代理进入冷却期
- **性能监控**：记录代理响应时间，优先选择快速代理
- **负载均衡**：基于响应时间和健康状态选择代理

### 2. 代理分离策略

在 `high_concurrency_crawler.py` 中实现：

- **Reese84代理**：专门用于生成reese84令牌
- **API代理**：专门用于API调用，避免与reese84冲突
- **智能选择**：优先选择不同的代理，避免资源竞争

### 3. 健康状态跟踪

- **失败阈值**：连续失败3次后进入冷却期
- **冷却时间**：120秒冷却期，期间不使用该代理
- **自动恢复**：冷却期结束后自动恢复可用状态
- **响应时间优化**：优先选择响应时间较快的代理

## 主要改进

### 代理选择逻辑

```python
# 优化前：随机选择代理
current_attempt_proxy_dict = random.choice(self.api_client_config_template.proxies)

# 优化后：智能选择健康代理
reese84_proxy_dict = self._get_healthy_proxy()
api_proxy_dict = self._get_healthy_proxy(exclude_proxy=reese84_proxy_dict)
```

### 结果记录

```python
# 记录代理使用结果，包含响应时间
self._record_proxy_result(api_proxy_dict, True, api_response_time)
```

### 健康检查

```python
# 自动排除不健康的代理
if health.get("failures", 0) < self.failure_threshold:
    available_proxies.append((proxy, health.get("avg_response_time", 999)))
```

## 配置参数

### proxy_optimization_config.json

- `failure_threshold`: 失败阈值 (默认: 3)
- `cooldown_duration`: 冷却时间 (默认: 120秒)
- `health_check_interval`: 健康检查间隔 (默认: 300秒)
- `max_response_time`: 最大响应时间 (默认: 10秒)

## 使用方法

### 1. 启动优化后的爬虫

```python
from high_concurrency_crawler import HighConcurrencyCrawler

# 创建爬虫实例（自动使用代理管理器）
crawler = HighConcurrencyCrawler(max_workers=50, max_retries=3)

# 添加抓取目标
crawler.add_target("SYD", "MEL", "20250301", "VA123")

# 开始抓取
crawler.start(task_id_for_redis="test_task", initial_api_client_template=api_client)
```

### 2. 监控代理状态

```python
from proxy_manager import get_proxy_manager

pm = get_proxy_manager()
stats = pm.get_proxy_stats()
print(f"代理统计: {stats}")
```

### 3. 手动重置代理

```python
# 重置特定代理
pm.reset_proxy_health("http://proxy_ip:port")

# 重置所有代理
pm.reset_proxy_health()
```

## 测试验证

运行测试脚本验证优化效果：

```bash
python test_proxy_optimization.py
```

测试内容包括：
- 代理管理器基本功能
- 代理分离策略
- 高负载场景模拟
- 代理恢复机制

## 预期效果

1. **提高成功率**：通过排除失败代理，提高整体请求成功率
2. **减少冲突**：分离reese84和API代理，避免资源竞争
3. **优化性能**：优先使用响应时间快的代理
4. **自动恢复**：失败代理自动进入冷却期并恢复
5. **负载均衡**：智能分配代理负载，避免过载

## 监控指标

- **总代理数**：配置的代理总数
- **健康代理数**：当前可用的健康代理数
- **冷却代理数**：处于冷却期的代理数
- **失败代理数**：连续失败的代理数
- **平均响应时间**：代理的平均响应时间

## 故障排除

### 常见问题

1. **无可用代理**
   - 检查proxy_config.json配置
   - 重置代理健康状态
   - 降低失败阈值

2. **代理响应慢**
   - 检查代理服务器状态
   - 调整max_response_time参数
   - 更换更快的代理

3. **频繁进入冷却期**
   - 检查代理质量
   - 调整failure_threshold参数
   - 增加代理数量

### 日志分析

查看日志中的代理使用情况：
- `Reese84代理: xxx, API代理: xxx`
- `代理 xxx 失败次数过多，进入冷却期`
- `警告: 无可用健康代理`

## 后续优化建议

1. **代理质量评估**：定期测试代理连通性和速度
2. **动态代理池**：支持运行时添加/删除代理
3. **地理位置优化**：根据目标服务器选择最近的代理
4. **成本优化**：根据代理成本和性能进行选择
5. **机器学习**：使用历史数据预测代理性能
