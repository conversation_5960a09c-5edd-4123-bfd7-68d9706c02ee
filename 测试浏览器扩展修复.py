#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器扩展修复效果
监听WebSocket连接，检查Cookie获取是否正常
"""

import asyncio
import websockets
import json
from datetime import datetime
import threading
import time


class ExtensionTestServer:
    def __init__(self):
        self.received_messages = []
        self.cookie_data_received = []
        self.status_updates = []
        self.connected_clients = set()
        self.server_running = False
    
    async def handle_client(self, websocket, path):
        """处理WebSocket客户端连接"""
        self.connected_clients.add(websocket)
        client_addr = websocket.remote_address
        print(f"🔗 浏览器扩展已连接: {client_addr}")
        
        try:
            async for message in websocket:
                await self.process_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            print(f"📱 浏览器扩展断开连接: {client_addr}")
        except Exception as e:
            print(f"❌ 处理扩展消息时出错: {e}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def process_message(self, websocket, message):
        """处理收到的消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type", "unknown")
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            print(f"[{timestamp}] 📨 收到消息: {message_type}")
            
            self.received_messages.append({
                "timestamp": timestamp,
                "type": message_type,
                "data": data
            })
            
            if message_type == "cookie_data":
                await self.handle_cookie_data(websocket, data)
            elif message_type == "status_update":
                await self.handle_status_update(websocket, data)
            else:
                print(f"⚠️  未知消息类型: {message_type}")
                await websocket.send(json.dumps({
                    "status": "error", 
                    "message": f"未知消息类型: {message_type}"
                }))
        
        except json.JSONDecodeError:
            print(f"❌ 无效的JSON消息: {message[:100]}...")
            await websocket.send(json.dumps({
                "status": "error", 
                "message": "无效的JSON格式"
            }))
        except Exception as e:
            print(f"❌ 处理消息时出错: {e}")
    
    async def handle_cookie_data(self, websocket, data):
        """处理Cookie数据"""
        print(f"🍪 收到Cookie数据!")
        
        # 提取关键信息
        payload = data.get("payload", [])
        execution_id = data.get("execution_id", "")
        user_agent = data.get("user_agent", "")
        source = data.get("source", "unknown")
        
        print(f"  来源: {source}")
        print(f"  ExecutionID: {'有' if execution_id else '无'} ({len(execution_id)} 字符)")
        print(f"  User-Agent: {'有' if user_agent else '无'}")
        print(f"  Cookie数量: {len(payload)}")
        
        # 分析重要Cookie
        important_cookies = []
        cookie_summary = {}
        
        for cookie in payload:
            if isinstance(cookie, dict):
                name = cookie.get("name", "")
                value = cookie.get("value", "")
                domain = cookie.get("domain", "")
                
                # 统计Cookie域名
                if domain not in cookie_summary:
                    cookie_summary[domain] = 0
                cookie_summary[domain] += 1
                
                # 检查重要的Cookie
                if name in ["DCSESSIONID", "IBEOpenToken", "reese84", "JSESSIONID"]:
                    important_cookies.append({
                        "name": name,
                        "value": value[:20] + "..." if len(value) > 20 else value,
                        "domain": domain
                    })
        
        print(f"  📊 Cookie域名分布:")
        for domain, count in cookie_summary.items():
            print(f"    {domain}: {count} 个")
        
        if important_cookies:
            print(f"  🎯 重要Cookie:")
            for cookie in important_cookies:
                print(f"    {cookie['name']}: {cookie['value']} ({cookie['domain']})")
        else:
            print(f"  ⚠️  未找到重要Cookie")
        
        # 保存Cookie数据
        self.cookie_data_received.append({
            "timestamp": datetime.now().isoformat(),
            "source": source,
            "execution_id": execution_id,
            "user_agent": user_agent,
            "cookie_count": len(payload),
            "important_cookies": important_cookies,
            "cookie_summary": cookie_summary
        })
        
        # 发送确认响应
        await websocket.send(json.dumps({
            "status": "success",
            "message": f"Cookie数据已收到并已开始调度处理"
        }))
        
        print(f"✅ Cookie数据处理完成")
    
    async def handle_status_update(self, websocket, data):
        """处理状态更新"""
        message = data.get("message", "")
        error = data.get("error", "")
        
        if error:
            print(f"❌ 扩展错误: {error}")
            self.status_updates.append({"type": "error", "message": error})
        else:
            print(f"ℹ️  扩展状态: {message}")
            self.status_updates.append({"type": "info", "message": message})
        
        # 发送确认
        await websocket.send(json.dumps({
            "status": "received",
            "message": "状态更新已收到"
        }))
    
    async def send_test_command(self):
        """发送测试命令给扩展"""
        if not self.connected_clients:
            print("⚠️  没有连接的扩展客户端")
            return
        
        test_command = {
            "action": "navigate_and_get_cookies",
            "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?ADT=1&class=Business",
            "source": "test_command"
        }
        
        print(f"📤 发送测试命令: navigate_and_get_cookies")
        
        for client in self.connected_clients.copy():
            try:
                await client.send(json.dumps(test_command))
                print(f"✅ 命令已发送给扩展")
            except Exception as e:
                print(f"❌ 发送命令失败: {e}")
                self.connected_clients.discard(client)
    
    def print_summary(self):
        """打印测试总结"""
        print(f"\n" + "="*60)
        print(f"📋 浏览器扩展测试总结")
        print(f"="*60)
        
        print(f"总消息数: {len(self.received_messages)}")
        print(f"Cookie数据: {len(self.cookie_data_received)} 次")
        print(f"状态更新: {len(self.status_updates)} 次")
        
        if self.cookie_data_received:
            print(f"\n🍪 Cookie获取详情:")
            for i, record in enumerate(self.cookie_data_received, 1):
                print(f"--- 记录 {i} ---")
                print(f"时间: {record['timestamp']}")
                print(f"来源: {record['source']}")
                print(f"Cookie数量: {record['cookie_count']}")
                print(f"ExecutionID: {'有' if record['execution_id'] else '无'}")
                print(f"重要Cookie: {len(record['important_cookies'])} 个")
                
                if record['important_cookies']:
                    for cookie in record['important_cookies']:
                        print(f"  - {cookie['name']}: {cookie['value']}")
        
        if self.status_updates:
            print(f"\n📊 状态更新:")
            error_count = sum(1 for update in self.status_updates if update['type'] == 'error')
            info_count = len(self.status_updates) - error_count
            print(f"信息: {info_count} 条")
            print(f"错误: {error_count} 条")
            
            if error_count > 0:
                print(f"\n❌ 错误详情:")
                for update in self.status_updates:
                    if update['type'] == 'error':
                        print(f"  - {update['message']}")
        
        # 分析结果
        print(f"\n🎯 测试结果分析:")
        if self.cookie_data_received:
            print("✅ Cookie获取功能正常")
            
            # 检查是否有重要Cookie
            has_important_cookies = any(
                len(record['important_cookies']) > 0 
                for record in self.cookie_data_received
            )
            
            if has_important_cookies:
                print("✅ 成功获取重要Cookie (DCSESSIONID等)")
            else:
                print("⚠️  未获取到重要Cookie，可能需要登录")
        else:
            print("❌ 未收到Cookie数据")
        
        error_count = sum(1 for update in self.status_updates if update['type'] == 'error')
        if error_count == 0:
            print("✅ 无严重错误")
        else:
            print(f"⚠️  发现 {error_count} 个错误")


async def main():
    """主函数"""
    print("🧪 浏览器扩展修复测试")
    print("="*60)
    print("此程序将启动WebSocket服务器，测试修复后的浏览器扩展")
    print("请按以下步骤操作:")
    print("1. 确保Firefox浏览器已安装并启用扩展")
    print("2. 访问Virgin Australia网站")
    print("3. 手动登录（如果需要）")
    print("4. 等待扩展自动连接和获取Cookie")
    print("="*60)
    
    server = ExtensionTestServer()
    
    try:
        # 启动WebSocket服务器
        print(f"🚀 启动WebSocket服务器 (端口: 8765)...")
        async with websockets.serve(server.handle_client, "127.0.0.1", 8765):
            server.server_running = True
            print(f"✅ 服务器运行中，等待扩展连接...")
            
            # 等待扩展连接
            print(f"⏰ 等待30秒，观察扩展行为...")
            await asyncio.sleep(10)
            
            # 如果有扩展连接，发送测试命令
            if server.connected_clients:
                print(f"🔗 检测到扩展连接，发送测试命令...")
                await server.send_test_command()
                
                # 等待响应
                print(f"⏰ 等待20秒，观察Cookie获取结果...")
                await asyncio.sleep(20)
            else:
                print(f"⚠️  未检测到扩展连接，继续等待...")
                await asyncio.sleep(20)
            
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断测试")
    except Exception as e:
        print(f"❌ 服务器运行出错: {e}")
    finally:
        server.print_summary()
        
        print(f"\n💡 修复效果评估:")
        if server.cookie_data_received:
            print("🎉 修复成功! 扩展可以正常获取Cookie")
        elif server.status_updates:
            print("⚠️  扩展有连接但Cookie获取可能有问题")
        else:
            print("❌ 扩展可能未正确连接或运行")
        
        print(f"\n📝 建议:")
        print("1. 检查Firefox扩展是否已启用")
        print("2. 确认Virgin Australia网站已登录")
        print("3. 查看浏览器控制台的错误信息")
        print("4. 重新加载扩展或重启浏览器")


if __name__ == "__main__":
    asyncio.run(main())
