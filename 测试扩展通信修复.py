#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扩展通信修复效果
专门测试内容脚本初始化和通信问题
"""

import asyncio
import websockets
import json
from datetime import datetime


async def test_extension_communication():
    """测试扩展通信修复"""
    print("🧪 测试扩展通信修复")
    print("="*50)
    
    communication_log = []
    script_status = {
        "injection": None,
        "ready_status": None,
        "ping_test": None,
        "communication": None,
        "cookie_received": False
    }
    
    async def handle_extension(websocket, path):
        print(f"🔗 扩展已连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                data = json.loads(message)
                message_type = data.get("type", "unknown")
                timestamp = datetime.now().strftime("%H:%M:%S")
                
                communication_log.append({
                    "time": timestamp,
                    "type": message_type,
                    "data": data
                })
                
                print(f"[{timestamp}] 📨 {message_type}")
                
                if message_type == "status_update":
                    message_text = data.get("message", "")
                    error_text = data.get("error", "")
                    
                    # 分析关键状态
                    if "Content script injected successfully" in message_text:
                        script_status["injection"] = "success"
                        print(f"   ✅ 脚本注入成功")
                    elif "Script check passed" in message_text:
                        script_status["ready_status"] = "success"
                        print(f"   ✅ 脚本状态检查通过")
                    elif "Ping test successful" in message_text:
                        script_status["ping_test"] = "success"
                        print(f"   ✅ Ping测试成功")
                    elif "Received response from content script" in message_text:
                        script_status["communication"] = "success"
                        print(f"   ✅ 内容脚本通信成功")
                    elif error_text:
                        print(f"   ❌ 错误: {error_text}")
                        if "内容脚本未正确加载" in error_text:
                            script_status["ready_status"] = "failed"
                        elif "Ping test failed" in error_text:
                            script_status["ping_test"] = "failed"
                        elif "内容脚本通信失败" in error_text:
                            script_status["communication"] = "failed"
                    else:
                        print(f"   ℹ️  {message_text}")
                
                elif message_type == "cookie_data":
                    script_status["cookie_received"] = True
                    payload = data.get("payload", [])
                    execution_id = data.get("execution_id", "")
                    print(f"   🍪 Cookie数据: {len(payload)} 个")
                    print(f"   📋 ExecutionID: {'有' if execution_id else '无'}")
                
                # 发送确认响应
                await websocket.send(json.dumps({
                    "status": "success",
                    "message": "测试服务器已收到消息"
                }))
        
        except websockets.exceptions.ConnectionClosed:
            print(f"📱 扩展断开连接")
        except Exception as e:
            print(f"❌ 处理消息出错: {e}")
    
    try:
        print("🚀 启动测试服务器 (端口: 8765)")
        async with websockets.serve(handle_extension, "127.0.0.1", 8765):
            print("✅ 测试服务器运行中")
            print("📋 请按以下步骤测试:")
            print("1. 重新加载Firefox扩展")
            print("2. 访问Virgin Australia网站")
            print("3. 观察扩展自动连接和通信")
            print("="*50)
            
            # 等待30秒观察结果
            await asyncio.sleep(30)
            
            # 分析测试结果
            print("\n📊 测试结果分析:")
            print("="*50)
            
            # 检查各个阶段的状态
            stages = [
                ("脚本注入", script_status["injection"]),
                ("脚本状态检查", script_status["ready_status"]),
                ("Ping测试", script_status["ping_test"]),
                ("内容脚本通信", script_status["communication"]),
                ("Cookie获取", "success" if script_status["cookie_received"] else "failed")
            ]
            
            success_count = 0
            for stage_name, status in stages:
                if status == "success":
                    print(f"✅ {stage_name}: 成功")
                    success_count += 1
                elif status == "failed":
                    print(f"❌ {stage_name}: 失败")
                else:
                    print(f"⚠️  {stage_name}: 未测试")
            
            print(f"\n📈 成功率: {success_count}/{len(stages)} ({success_count/len(stages)*100:.1f}%)")
            
            # 提供修复建议
            print(f"\n💡 修复建议:")
            if script_status["injection"] != "success":
                print("1. 检查扩展权限和manifest.json配置")
            elif script_status["ready_status"] != "success":
                print("1. 内容脚本全局变量设置有问题")
                print("2. 检查脚本加载时序")
            elif script_status["ping_test"] != "success":
                print("1. 消息监听器设置有问题")
                print("2. 检查API兼容性 (browser vs chrome)")
            elif script_status["communication"] != "success":
                print("1. 异步消息处理有问题")
                print("2. 检查sendResponse调用")
            elif not script_status["cookie_received"]:
                print("1. 登录状态检查失败")
                print("2. Cookie获取逻辑有问题")
            else:
                print("🎉 所有测试通过！扩展通信正常")
            
            # 显示通信日志摘要
            if communication_log:
                print(f"\n📝 通信日志摘要 (最近5条):")
                for log_entry in communication_log[-5:]:
                    print(f"  [{log_entry['time']}] {log_entry['type']}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_extension_communication())
